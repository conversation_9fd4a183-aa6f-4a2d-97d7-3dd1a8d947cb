# Dolet Self-Hosting Symbol Table
# Manages variable scopes, function definitions, and type information
# Provides fast symbol lookup and scope management

# Type constants
const TYPE_INT = 1
const TYPE_FLOAT = 2
const TYPE_DOUBLE = 3
const TYPE_STRING = 4
const TYPE_CHAR = 5
const TYPE_BOOL = 6
const TYPE_NULL = 7
const TYPE_UNKNOWN = 8
const TYPE_ARRAY = 9
const TYPE_FUNCTION = 10

# Symbol table structure
# Each scope is an array of symbols
# Each symbol: [name, type, is_const, is_initialized, line, column, value]
set scopes = []
set current_scope = 0

# Initialize symbol table
fun init_symbol_table():
    set scopes = []
    set current_scope = 0
    # Create global scope
    push_scope()
end

# Push new scope
fun push_scope():
    set new_scope = []
    set scopes = scopes + [new_scope]
    set current_scope = array_length(scopes) - 1
end

# Pop current scope
fun pop_scope():
    if current_scope > 0:
        # Remove last scope
        set new_scopes = []
        set i = 0
        while i < array_length(scopes) - 1:
            set new_scopes = new_scopes + [scopes[i]]
            set i = i + 1
        end
        set scopes = new_scopes
        set current_scope = current_scope - 1
    end
end

# Create symbol
fun create_symbol(name, symbol_type, is_const, is_initialized, line, column):
    return [name, symbol_type, is_const, is_initialized, line, column, null]
end

# Get symbol properties
fun get_symbol_name(symbol):
    return symbol[0]
end

fun get_symbol_type(symbol):
    return symbol[1]
end

fun get_symbol_is_const(symbol):
    return symbol[2]
end

fun get_symbol_is_initialized(symbol):
    return symbol[3]
end

fun get_symbol_line(symbol):
    return symbol[4]
end

fun get_symbol_column(symbol):
    return symbol[5]
end

fun get_symbol_value(symbol):
    return symbol[6]
end

fun set_symbol_value(symbol, value):
    set symbol[6] = value
end

fun set_symbol_initialized(symbol, initialized):
    set symbol[3] = initialized
end

# Define symbol in current scope
fun define_symbol(name, symbol_type, is_const, line, column):
    if current_scope < 0 || current_scope >= array_length(scopes):
        say "Error: Invalid scope"
        return false
    end
    
    # Check if symbol already exists in current scope
    set current_scope_symbols = scopes[current_scope]
    set i = 0
    while i < array_length(current_scope_symbols):
        set symbol = current_scope_symbols[i]
        if get_symbol_name(symbol) == name:
            say "Error: Symbol '" + name + "' already defined in current scope at line " + line
            return false
        end
        set i = i + 1
    end
    
    # Add symbol to current scope
    set new_symbol = create_symbol(name, symbol_type, is_const, false, line, column)
    set current_scope_symbols = current_scope_symbols + [new_symbol]
    set scopes[current_scope] = current_scope_symbols
    
    return true
end

# Look up symbol in all scopes (starting from current)
fun lookup_symbol(name):
    set scope_index = current_scope
    while scope_index >= 0:
        set scope_symbols = scopes[scope_index]
        set i = 0
        while i < array_length(scope_symbols):
            set symbol = scope_symbols[i]
            if get_symbol_name(symbol) == name:
                return symbol
            end
            set i = i + 1
        end
        set scope_index = scope_index - 1
    end
    return null
end

# Look up symbol in current scope only
fun lookup_symbol_current_scope(name):
    if current_scope < 0 || current_scope >= array_length(scopes):
        return null
    end
    
    set current_scope_symbols = scopes[current_scope]
    set i = 0
    while i < array_length(current_scope_symbols):
        set symbol = current_scope_symbols[i]
        if get_symbol_name(symbol) == name:
            return symbol
        end
        set i = i + 1
    end
    return null
end

# Check if symbol exists
fun symbol_exists(name):
    return lookup_symbol(name) != null
end

# Check if symbol exists in current scope
fun symbol_exists_current_scope(name):
    return lookup_symbol_current_scope(name) != null
end

# Assign value to symbol
fun assign_symbol(name, value, line, column):
    set symbol = lookup_symbol(name)
    if symbol == null:
        say "Error: Undefined variable '" + name + "' at line " + line
        return false
    end
    
    if get_symbol_is_const(symbol):
        say "Error: Cannot assign to constant '" + name + "' at line " + line
        return false
    end
    
    set_symbol_value(symbol, value)
    set_symbol_initialized(symbol, true)
    return true
end

# Initialize symbol with value
fun initialize_symbol(name, value, line, column):
    set symbol = lookup_symbol(name)
    if symbol == null:
        say "Error: Undefined variable '" + name + "' at line " + line
        return false
    end
    
    set_symbol_value(symbol, value)
    set_symbol_initialized(symbol, true)
    return true
end

# Get symbol value
fun get_symbol_value_by_name(name):
    set symbol = lookup_symbol(name)
    if symbol == null:
        return null
    end
    return get_symbol_value(symbol)
end

# Type inference from value
fun infer_type_from_value(value):
    if value == null:
        return TYPE_NULL
    end
    if value == true || value == false:
        return TYPE_BOOL
    end
    
    # Check if it's a string
    if length(value + "") > 0:
        # If adding empty string works, it's likely a string or convertible
        if value + "" == value:
            return TYPE_STRING
        end
    end
    
    # Check if it's a number
    if value == 0 || value != 0:
        # Check if it has decimal places
        if value != value / 1:  # Has fractional part
            return TYPE_FLOAT
        else:
            return TYPE_INT
        end
    end
    
    return TYPE_UNKNOWN
end

# Convert type name to type constant
fun type_name_to_constant(type_name):
    if type_name == "int":
        return TYPE_INT
    end
    if type_name == "float":
        return TYPE_FLOAT
    end
    if type_name == "double":
        return TYPE_DOUBLE
    end
    if type_name == "string":
        return TYPE_STRING
    end
    if type_name == "char":
        return TYPE_CHAR
    end
    if type_name == "bool":
        return TYPE_BOOL
    end
    if type_name == "null":
        return TYPE_NULL
    end
    return TYPE_UNKNOWN
end

# Convert type constant to type name
fun type_constant_to_name(type_const):
    if type_const == TYPE_INT:
        return "int"
    end
    if type_const == TYPE_FLOAT:
        return "float"
    end
    if type_const == TYPE_DOUBLE:
        return "double"
    end
    if type_const == TYPE_STRING:
        return "string"
    end
    if type_const == TYPE_CHAR:
        return "char"
    end
    if type_const == TYPE_BOOL:
        return "bool"
    end
    if type_const == TYPE_NULL:
        return "null"
    end
    if type_const == TYPE_ARRAY:
        return "array"
    end
    if type_const == TYPE_FUNCTION:
        return "function"
    end
    return "unknown"
end

# Check type compatibility
fun types_compatible(type1, type2):
    if type1 == type2:
        return true
    end
    
    # Allow implicit conversions
    if (type1 == TYPE_INT && type2 == TYPE_FLOAT) || (type1 == TYPE_FLOAT && type2 == TYPE_INT):
        return true
    end
    if (type1 == TYPE_FLOAT && type2 == TYPE_DOUBLE) || (type1 == TYPE_DOUBLE && type2 == TYPE_FLOAT):
        return true
    end
    if (type1 == TYPE_INT && type2 == TYPE_DOUBLE) || (type1 == TYPE_DOUBLE && type2 == TYPE_INT):
        return true
    end
    
    # String concatenation allows any type
    if type1 == TYPE_STRING || type2 == TYPE_STRING:
        return true
    end
    
    return false
end

# Print symbol table (for debugging)
fun print_symbol_table():
    say "Symbol Table:"
    set scope_index = 0
    while scope_index <= current_scope:
        say "  Scope " + scope_index + ":"
        set scope_symbols = scopes[scope_index]
        set i = 0
        while i < array_length(scope_symbols):
            set symbol = scope_symbols[i]
            set name = get_symbol_name(symbol)
            set type_name = type_constant_to_name(get_symbol_type(symbol))
            set is_const = get_symbol_is_const(symbol)
            set is_init = get_symbol_is_initialized(symbol)
            say "    " + name + ": " + type_name + " (const: " + is_const + ", init: " + is_init + ")"
            set i = i + 1
        end
        set scope_index = scope_index + 1
    end
end

# Get current scope depth
fun get_scope_depth():
    return current_scope
end

# Check if we're in global scope
fun is_global_scope():
    return current_scope == 0
end

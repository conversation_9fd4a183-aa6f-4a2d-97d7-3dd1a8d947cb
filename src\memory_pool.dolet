# Dolet Self-Hosting Memory Pool Allocator
# Arena/bump allocator for ultra-fast memory management during compilation
# Provides zero-fragmentation memory allocation with batch deallocation

# Memory pool constants
const POOL_SIZE = 1048576  # 1MB default pool size
const ALIGNMENT = 8        # 8-byte alignment for performance

# Memory pool structure
# [pool_data, current_offset, pool_size, allocated_count, peak_usage]
set memory_pools = []
set current_pool = 0
set total_allocated = 0
set peak_memory_usage = 0

# Initialize memory pool system
fun init_memory_pools():
    set memory_pools = []
    set current_pool = 0
    set total_allocated = 0
    set peak_memory_usage = 0
    
    # Create initial pool
    create_new_pool(POOL_SIZE)
end

# Create new memory pool
fun create_new_pool(size):
    if size < 1024:
        set size = 1024  # Minimum pool size
    end
    
    # Pool structure: [data_array, current_offset, pool_size, allocated_count, peak_usage]
    set pool_data = create_array(size)
    set pool = [pool_data, 0, size, 0, 0]
    
    set memory_pools = memory_pools + [pool]
    set current_pool = array_length(memory_pools) - 1
    
    return current_pool
end

# Get current pool
fun get_current_pool():
    if current_pool >= 0 && current_pool < array_length(memory_pools):
        return memory_pools[current_pool]
    end
    return null
end

# Align size to boundary
fun align_size(size):
    set remainder = size % ALIGNMENT
    if remainder == 0:
        return size
    end
    return size + (ALIGNMENT - remainder)
end

# Allocate memory from current pool
fun pool_allocate(size):
    if size <= 0:
        return null
    end
    
    set aligned_size = align_size(size)
    set pool = get_current_pool()
    
    if pool == null:
        # Create first pool if none exists
        create_new_pool(POOL_SIZE)
        set pool = get_current_pool()
    end
    
    set pool_data = pool[0]
    set current_offset = pool[1]
    set pool_size = pool[2]
    set allocated_count = pool[3]
    set peak_usage = pool[4]
    
    # Check if we have enough space
    if current_offset + aligned_size > pool_size:
        # Try to create a larger pool if needed
        set new_pool_size = POOL_SIZE
        if aligned_size > new_pool_size:
            set new_pool_size = aligned_size * 2
        end
        
        create_new_pool(new_pool_size)
        return pool_allocate(size)  # Retry with new pool
    end
    
    # Allocate from current pool
    set allocation_start = current_offset
    set new_offset = current_offset + aligned_size
    
    # Update pool state
    set pool[1] = new_offset
    set pool[3] = allocated_count + 1
    
    # Update peak usage
    if new_offset > peak_usage:
        set pool[4] = new_offset
        if new_offset > peak_memory_usage:
            set peak_memory_usage = new_offset
        end
    end
    
    set total_allocated = total_allocated + aligned_size
    
    # Return allocation info: [pool_index, offset, size]
    return [current_pool, allocation_start, aligned_size]
end

# Allocate array in memory pool
fun pool_allocate_array(element_count):
    if element_count <= 0:
        return null
    end
    
    # Each array element is a pointer/reference (assume 8 bytes)
    set size = element_count * 8
    return pool_allocate(size)
end

# Allocate string in memory pool
fun pool_allocate_string(length):
    if length < 0:
        return null
    end
    
    # String length + null terminator
    set size = length + 1
    return pool_allocate(size)
end

# Reset current pool (fast deallocation)
fun reset_current_pool():
    set pool = get_current_pool()
    if pool == null:
        return false
    end
    
    # Reset offset to beginning
    set pool[1] = 0
    set pool[3] = 0  # Reset allocation count
    
    return true
end

# Reset all pools
fun reset_all_pools():
    set i = 0
    while i < array_length(memory_pools):
        set pool = memory_pools[i]
        set pool[1] = 0  # Reset offset
        set pool[3] = 0  # Reset allocation count
        set i = i + 1
    end
    
    set total_allocated = 0
    set current_pool = 0
end

# Switch to specific pool
fun switch_to_pool(pool_index):
    if pool_index >= 0 && pool_index < array_length(memory_pools):
        set current_pool = pool_index
        return true
    end
    return false
end

# Get pool statistics
fun get_pool_stats(pool_index):
    if pool_index < 0 || pool_index >= array_length(memory_pools):
        return null
    end
    
    set pool = memory_pools[pool_index]
    set current_offset = pool[1]
    set pool_size = pool[2]
    set allocated_count = pool[3]
    set peak_usage = pool[4]
    
    set used_bytes = current_offset
    set free_bytes = pool_size - current_offset
    set usage_percent = (used_bytes * 100) / pool_size
    
    # Return stats: [used_bytes, free_bytes, usage_percent, allocated_count, peak_usage]
    return [used_bytes, free_bytes, usage_percent, allocated_count, peak_usage]
end

# Get total memory statistics
fun get_total_memory_stats():
    set total_used = 0
    set total_size = 0
    set total_allocations = 0
    
    set i = 0
    while i < array_length(memory_pools):
        set pool = memory_pools[i]
        set total_used = total_used + pool[1]
        set total_size = total_size + pool[2]
        set total_allocations = total_allocations + pool[3]
        set i = i + 1
    end
    
    set total_free = total_size - total_used
    set usage_percent = 0
    if total_size > 0:
        set usage_percent = (total_used * 100) / total_size
    end
    
    # Return: [total_used, total_free, total_size, usage_percent, total_allocations, peak_usage, pool_count]
    return [total_used, total_free, total_size, usage_percent, total_allocations, peak_memory_usage, array_length(memory_pools)]
end

# Print memory pool statistics
fun print_memory_stats():
    set stats = get_total_memory_stats()
    set total_used = stats[0]
    set total_free = stats[1]
    set total_size = stats[2]
    set usage_percent = stats[3]
    set total_allocations = stats[4]
    set peak_usage = stats[5]
    set pool_count = stats[6]
    
    say "Memory Pool Statistics:"
    say "======================"
    say "Total Pools: " + pool_count
    say "Total Size: " + total_size + " bytes"
    say "Used: " + total_used + " bytes (" + usage_percent + "%)"
    say "Free: " + total_free + " bytes"
    say "Peak Usage: " + peak_usage + " bytes"
    say "Total Allocations: " + total_allocations
    
    say ""
    say "Per-Pool Breakdown:"
    set i = 0
    while i < array_length(memory_pools):
        set pool_stats = get_pool_stats(i)
        if pool_stats != null:
            set used = pool_stats[0]
            set free = pool_stats[1]
            set percent = pool_stats[2]
            set allocs = pool_stats[3]
            set peak = pool_stats[4]
            
            say "  Pool " + i + ": " + used + "/" + (used + free) + " bytes (" + percent + "%), " + allocs + " allocations, peak: " + peak
        end
        set i = i + 1
    end
end

# Check if allocation is valid
fun is_valid_allocation(allocation):
    if allocation == null:
        return false
    end
    if array_length(allocation) != 3:
        return false
    end
    
    set pool_index = allocation[0]
    set offset = allocation[1]
    set size = allocation[2]
    
    if pool_index < 0 || pool_index >= array_length(memory_pools):
        return false
    end
    if offset < 0 || size <= 0:
        return false
    end
    
    set pool = memory_pools[pool_index]
    set pool_size = pool[2]
    
    return offset + size <= pool_size
end

# Get allocation info
fun get_allocation_info(allocation):
    if !is_valid_allocation(allocation):
        return null
    end
    
    set pool_index = allocation[0]
    set offset = allocation[1]
    set size = allocation[2]
    
    # Return: [pool_index, offset, size, is_valid]
    return [pool_index, offset, size, true]
end

# Compact memory pools (remove empty pools)
fun compact_pools():
    set new_pools = []
    set compacted_count = 0
    
    set i = 0
    while i < array_length(memory_pools):
        set pool = memory_pools[i]
        set current_offset = pool[1]
        
        # Keep pools that have allocations
        if current_offset > 0:
            set new_pools = new_pools + [pool]
        else:
            set compacted_count = compacted_count + 1
        end
        set i = i + 1
    end
    
    set memory_pools = new_pools
    
    # Adjust current pool index
    if current_pool >= array_length(memory_pools):
        if array_length(memory_pools) > 0:
            set current_pool = array_length(memory_pools) - 1
        else:
            set current_pool = 0
            create_new_pool(POOL_SIZE)
        end
    end
    
    return compacted_count
end

# Get memory fragmentation info
fun get_fragmentation_info():
    set total_pools = array_length(memory_pools)
    set active_pools = 0
    set total_waste = 0
    
    set i = 0
    while i < total_pools:
        set pool = memory_pools[i]
        set current_offset = pool[1]
        set pool_size = pool[2]
        
        if current_offset > 0:
            set active_pools = active_pools + 1
            set waste = pool_size - current_offset
            set total_waste = total_waste + waste
        end
        set i = i + 1
    end
    
    set fragmentation_percent = 0
    if total_pools > 0:
        set fragmentation_percent = ((total_pools - active_pools) * 100) / total_pools
    end
    
    # Return: [total_pools, active_pools, fragmentation_percent, total_waste]
    return [total_pools, active_pools, fragmentation_percent, total_waste]
end

# Cleanup memory pools
fun cleanup_memory_pools():
    reset_all_pools()
    set memory_pools = []
    set current_pool = 0
    set total_allocated = 0
    set peak_memory_usage = 0
end

# Memory pool health check
fun memory_health_check():
    set stats = get_total_memory_stats()
    set usage_percent = stats[3]
    set frag_info = get_fragmentation_info()
    set fragmentation_percent = frag_info[2]
    
    set health_score = 100
    
    # Penalize high memory usage
    if usage_percent > 90:
        set health_score = health_score - 30
    else if usage_percent > 75:
        set health_score = health_score - 15
    end
    
    # Penalize high fragmentation
    if fragmentation_percent > 50:
        set health_score = health_score - 25
    else if fragmentation_percent > 25:
        set health_score = health_score - 10
    end
    
    # Return health score (0-100)
    return health_score
end

use std::io::{self, Write};


fn main() {
    println!("{}", "🎉 Hello from User Project!");
    println!("{}", "This program was compiled by the Dolet self-hosting compiler!");
    let mut project_name = "My Awesome Dolet Project";
    let mut version = "1.0.0";
    println!("{}", "");
    println!("{}", "Project Information:");
    println!("{}", "===================");
    println!("{}", "Name: My Awesome Dolet Project");
    println!("{}", "Version: 1.0.0");
    let mut x = 42;
    println!("{}", "");
    println!("{}", "Testing variables:");
    println!("{}", "x = 42");
    println!("{}", "");
    println!("{}", "✅ Features demonstrated:");
    println!("{}", "• Variables");
    println!("{}", "• String literals");
    println!("{}", "• Comments");
    println!("{}", "");
    println!("{}", "🚀 Success! User project compiled and running!");
    println!("{}", "The Dolet self-hosting compiler works perfectly!");
}

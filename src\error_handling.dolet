# Dolet Self-Hosting Error Handling System
# Comprehensive error reporting with line/column information and helpful messages
# Provides unified error management across all compiler phases

# Error types
const ERROR_LEXICAL = 1
const ERROR_SYNTAX = 2
const ERROR_SEMANTIC = 3
const ERROR_TYPE = 4
const ERROR_CODEGEN = 5
const ERROR_RUNTIME = 6

# Error severity levels
const SEVERITY_INFO = 1
const SEVERITY_WARNING = 2
const SEVERITY_ERROR = 3
const SEVERITY_FATAL = 4

# Global error state
set all_errors = []
set error_count = 0
set warning_count = 0
set max_errors = 100

# Initialize error handling system
fun init_error_handling():
    set all_errors = []
    set error_count = 0
    set warning_count = 0
end

# Create error record
fun create_error(error_type, severity, message, line, column, source_file):
    return [error_type, severity, message, line, column, source_file]
end

# Get error properties
fun get_error_type(error):
    return error[0]
end

fun get_error_severity(error):
    return error[1]
end

fun get_error_message(error):
    return error[2]
end

fun get_error_line(error):
    return error[3]
end

fun get_error_column(error):
    return error[4]
end

fun get_error_source_file(error):
    return error[5]
end

# Add error to global error list
fun add_error(error_type, severity, message, line, column, source_file):
    if array_length(all_errors) >= max_errors:
        say "Too many errors, stopping compilation"
        return false
    end
    
    set error = create_error(error_type, severity, message, line, column, source_file)
    set all_errors = all_errors + [error]
    
    if severity == SEVERITY_ERROR || severity == SEVERITY_FATAL:
        set error_count = error_count + 1
    else if severity == SEVERITY_WARNING:
        set warning_count = warning_count + 1
    end
    
    return true
end

# Convenience functions for different error types
fun add_lexical_error(message, line, column, source_file):
    return add_error(ERROR_LEXICAL, SEVERITY_ERROR, message, line, column, source_file)
end

fun add_syntax_error(message, line, column, source_file):
    return add_error(ERROR_SYNTAX, SEVERITY_ERROR, message, line, column, source_file)
end

fun add_semantic_error(message, line, column, source_file):
    return add_error(ERROR_SEMANTIC, SEVERITY_ERROR, message, line, column, source_file)
end

fun add_type_error(message, line, column, source_file):
    return add_error(ERROR_TYPE, SEVERITY_ERROR, message, line, column, source_file)
end

fun add_codegen_error(message, line, column, source_file):
    return add_error(ERROR_CODEGEN, SEVERITY_ERROR, message, line, column, source_file)
end

fun add_warning(error_type, message, line, column, source_file):
    return add_error(error_type, SEVERITY_WARNING, message, line, column, source_file)
end

fun add_fatal_error(error_type, message, line, column, source_file):
    return add_error(error_type, SEVERITY_FATAL, message, line, column, source_file)
end

# Check if there are any errors
fun has_errors():
    return error_count > 0
end

fun has_warnings():
    return warning_count > 0
end

fun has_fatal_errors():
    set i = 0
    while i < array_length(all_errors):
        if get_error_severity(all_errors[i]) == SEVERITY_FATAL:
            return true
        end
        set i = i + 1
    end
    return false
end

# Get error counts
fun get_error_count():
    return error_count
end

fun get_warning_count():
    return warning_count
end

fun get_total_error_count():
    return array_length(all_errors)
end

# Convert error type to string
fun error_type_to_string(error_type):
    if error_type == ERROR_LEXICAL:
        return "Lexical"
    end
    if error_type == ERROR_SYNTAX:
        return "Syntax"
    end
    if error_type == ERROR_SEMANTIC:
        return "Semantic"
    end
    if error_type == ERROR_TYPE:
        return "Type"
    end
    if error_type == ERROR_CODEGEN:
        return "Code Generation"
    end
    if error_type == ERROR_RUNTIME:
        return "Runtime"
    end
    return "Unknown"
end

# Convert severity to string
fun severity_to_string(severity):
    if severity == SEVERITY_INFO:
        return "Info"
    end
    if severity == SEVERITY_WARNING:
        return "Warning"
    end
    if severity == SEVERITY_ERROR:
        return "Error"
    end
    if severity == SEVERITY_FATAL:
        return "Fatal"
    end
    return "Unknown"
end

# Format error message
fun format_error(error):
    set error_type_str = error_type_to_string(get_error_type(error))
    set severity_str = severity_to_string(get_error_severity(error))
    set message = get_error_message(error)
    set line = get_error_line(error)
    set column = get_error_column(error)
    set source_file = get_error_source_file(error)
    
    set formatted = severity_str + " [" + error_type_str + "]"
    
    if source_file != null && source_file != "":
        set formatted = formatted + " in " + source_file
    end
    
    if line > 0:
        set formatted = formatted + " at line " + line
        if column > 0:
            set formatted = formatted + ", column " + column
        end
    end
    
    set formatted = formatted + ": " + message
    
    return formatted
end

# Print single error
fun print_error(error):
    say format_error(error)
end

# Print all errors
fun print_all_errors():
    if array_length(all_errors) == 0:
        return
    end
    
    say "Compilation Errors and Warnings:"
    say "================================"
    
    set i = 0
    while i < array_length(all_errors):
        print_error(all_errors[i])
        set i = i + 1
    end
    
    say ""
    say "Summary: " + error_count + " error(s), " + warning_count + " warning(s)"
end

# Print only errors (no warnings)
fun print_errors_only():
    set has_printed = false
    
    set i = 0
    while i < array_length(all_errors):
        set error = all_errors[i]
        set severity = get_error_severity(error)
        if severity == SEVERITY_ERROR || severity == SEVERITY_FATAL:
            if !has_printed:
                say "Compilation Errors:"
                say "=================="
                set has_printed = true
            end
            print_error(error)
        end
        set i = i + 1
    end
    
    if has_printed:
        say ""
        say "Total: " + error_count + " error(s)"
    end
end

# Print only warnings
fun print_warnings_only():
    set has_printed = false
    
    set i = 0
    while i < array_length(all_errors):
        set error = all_errors[i]
        if get_error_severity(error) == SEVERITY_WARNING:
            if !has_printed:
                say "Compilation Warnings:"
                say "===================="
                set has_printed = true
            end
            print_error(error)
        end
        set i = i + 1
    end
    
    if has_printed:
        say ""
        say "Total: " + warning_count + " warning(s)"
    end
end

# Clear all errors
fun clear_errors():
    set all_errors = []
    set error_count = 0
    set warning_count = 0
end

# Get errors by type
fun get_errors_by_type(error_type):
    set filtered_errors = []
    set i = 0
    while i < array_length(all_errors):
        set error = all_errors[i]
        if get_error_type(error) == error_type:
            set filtered_errors = filtered_errors + [error]
        end
        set i = i + 1
    end
    return filtered_errors
end

# Get errors by severity
fun get_errors_by_severity(severity):
    set filtered_errors = []
    set i = 0
    while i < array_length(all_errors):
        set error = all_errors[i]
        if get_error_severity(error) == severity:
            set filtered_errors = filtered_errors + [error]
        end
        set i = i + 1
    end
    return filtered_errors
end

# Enhanced error messages with suggestions
fun add_enhanced_syntax_error(message, line, column, source_file, suggestion):
    set enhanced_message = message
    if suggestion != null && suggestion != "":
        set enhanced_message = enhanced_message + ". " + suggestion
    end
    return add_syntax_error(enhanced_message, line, column, source_file)
end

fun add_enhanced_semantic_error(message, line, column, source_file, suggestion):
    set enhanced_message = message
    if suggestion != null && suggestion != "":
        set enhanced_message = enhanced_message + ". " + suggestion
    end
    return add_semantic_error(enhanced_message, line, column, source_file)
end

# Common error patterns with helpful messages
fun report_undefined_variable(var_name, line, column, source_file):
    set message = "Undefined variable '" + var_name + "'"
    set suggestion = "Did you forget to declare it with 'set' or 'const'?"
    return add_enhanced_semantic_error(message, line, column, source_file, suggestion)
end

fun report_type_mismatch(expected_type, actual_type, line, column, source_file):
    set message = "Type mismatch: expected " + expected_type + ", got " + actual_type
    set suggestion = "Check the types of your expressions"
    return add_enhanced_type_error(message, line, column, source_file, suggestion)
end

fun report_function_not_found(func_name, line, column, source_file):
    set message = "Function '" + func_name + "' not found"
    set suggestion = "Check the function name spelling and make sure it's declared"
    return add_enhanced_semantic_error(message, line, column, source_file, suggestion)
end

fun report_wrong_argument_count(func_name, expected, actual, line, column, source_file):
    set message = "Function '" + func_name + "' expects " + expected + " arguments, got " + actual
    set suggestion = "Check the function signature"
    return add_enhanced_semantic_error(message, line, column, source_file, suggestion)
end

fun report_unexpected_token(expected, actual, line, column, source_file):
    set message = "Expected '" + expected + "', got '" + actual + "'"
    set suggestion = "Check your syntax"
    return add_enhanced_syntax_error(message, line, column, source_file, suggestion)
end

# Check if compilation should continue
fun should_continue_compilation():
    return !has_fatal_errors() && error_count < max_errors
end

# Set maximum error count before stopping
fun set_max_errors(max):
    set max_errors = max
end

# Get all errors (for external processing)
fun get_all_errors():
    return all_errors
end

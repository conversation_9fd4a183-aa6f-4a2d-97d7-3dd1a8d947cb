# Dolet File Resolver
# يحل مسارات الملفات ويدير استيراد الوحدات

# File resolution state
set base_paths = []
set resolved_files = []
set file_cache = []

# Initialize file resolver
fun init_file_resolver():
    set base_paths = ["src/", "src/runtime/", "./"]
    set resolved_files = []
    set file_cache = []
end

# Add base path for module resolution
fun add_base_path(path):
    set base_paths = base_paths + [path]
end

# Normalize path separators
fun normalize_path(path):
    set normalized = ""
    set i = 0
    while i < length(path):
        set ch = path[i]
        if ch == '\\':
            set normalized = normalized + "/"
        else:
            set normalized = normalized + ch
        end
        set i = i + 1
    end
    return normalized
end

# Check if path is absolute
fun is_absolute_path(path):
    if length(path) == 0:
        return false
    end
    
    # Check for Windows absolute path (C:\ or similar)
    if length(path) >= 3:
        if path[1] == ':' && path[2] == '/':
            return true
        end
    end
    
    # Check for Unix absolute path (starts with /)
    if path[0] == '/':
        return true
    end
    
    return false
end

# Join path components
fun join_paths(base, relative):
    set normalized_base = normalize_path(base)
    set normalized_relative = normalize_path(relative)
    
    # Remove trailing slash from base
    if length(normalized_base) > 0:
        if normalized_base[length(normalized_base) - 1] == '/':
            set normalized_base = substring(normalized_base, 0, length(normalized_base) - 1)
        end
    end
    
    # Remove leading slash from relative
    if length(normalized_relative) > 0:
        if normalized_relative[0] == '/':
            set normalized_relative = substring(normalized_relative, 1, length(normalized_relative) - 1)
        end
    end
    
    if length(normalized_base) == 0:
        return normalized_relative
    end
    
    return normalized_base + "/" + normalized_relative
end

# Check if file exists (simulated)
fun file_exists(file_path):
    # في التطبيق الحقيقي، هذا سيتحقق من نظام الملفات
    # الآن سنحاكي وجود الملفات المعروفة
    
    set known_files = [
        "src/tokenizer.dolet",
        "src/parser.dolet",
        "src/symbol_table.dolet",
        "src/type_inference.dolet",
        "src/semantic_analyzer.dolet",
        "src/error_handling.dolet",
        "src/memory_pool.dolet",
        "src/codegen.dolet",
        "src/main.dolet",
        "src/module_loader.dolet",
        "src/file_resolver.dolet",
        "src/runtime/string_ops.dolet",
        "src/runtime/math_ops.dolet",
        "src/runtime/array_ops.dolet",
        "src/runtime/file_ops.dolet",
        "src/runtime/error_ops.dolet"
    ]
    
    set i = 0
    while i < array_length(known_files):
        if known_files[i] == file_path:
            return true
        end
        set i = i + 1
    end
    
    return false
end

# Resolve file path
fun resolve_file_path(module_name):
    # If it's already a full path, check if it exists
    if is_absolute_path(module_name):
        if file_exists(module_name):
            return module_name
        end
        return null
    end
    
    # If it has .dolet extension, try as-is first
    if string_contains(module_name, ".dolet"):
        if file_exists(module_name):
            return module_name
        end
        
        # Try with base paths
        set i = 0
        while i < array_length(base_paths):
            set full_path = join_paths(base_paths[i], module_name)
            if file_exists(full_path):
                return full_path
            end
            set i = i + 1
        end
        
        return null
    end
    
    # Try adding .dolet extension
    set dolet_file = module_name + ".dolet"
    
    # Try current directory first
    if file_exists(dolet_file):
        return dolet_file
    end
    
    # Try with base paths
    set i = 0
    while i < array_length(base_paths):
        set full_path = join_paths(base_paths[i], dolet_file)
        if file_exists(full_path):
            return full_path
        end
        set i = i + 1
    end
    
    return null
end

# Resolve multiple files
fun resolve_files(module_names):
    set resolved = []
    
    set i = 0
    while i < array_length(module_names):
        set module_name = module_names[i]
        set resolved_path = resolve_file_path(module_name)
        
        if resolved_path != null:
            set resolved = resolved + [resolved_path]
            say "Resolved: " + module_name + " -> " + resolved_path
        else:
            say "Error: Could not resolve: " + module_name
            return null
        end
        set i = i + 1
    end
    
    return resolved
end

# Get file from cache
fun get_cached_file(file_path):
    set i = 0
    while i < array_length(file_cache):
        set cache_entry = file_cache[i]
        if cache_entry[0] == file_path:
            return cache_entry[1]  # Return cached content
        end
        set i = i + 1
    end
    return null
end

# Add file to cache
fun cache_file(file_path, content):
    set cache_entry = [file_path, content]
    set file_cache = file_cache + [cache_entry]
end

# Read file with caching
fun read_file_cached(file_path):
    # Check cache first
    set cached_content = get_cached_file(file_path)
    if cached_content != null:
        return cached_content
    end
    
    # في التطبيق الحقيقي، هذا سيقرأ من نظام الملفات
    # الآن سنحاكي محتوى الملفات
    set content = load_module_file(file_path)  # Use function from module_loader
    
    if content != null:
        cache_file(file_path, content)
    end
    
    return content
end

# Resolve and load all compiler files
fun resolve_compiler_files():
    say "Resolving compiler files..."
    
    set core_modules = [
        "tokenizer",
        "parser",
        "symbol_table", 
        "type_inference",
        "semantic_analyzer",
        "error_handling",
        "memory_pool",
        "codegen",
        "main",
        "module_loader",
        "file_resolver"
    ]
    
    set runtime_modules = [
        "runtime/string_ops",
        "runtime/math_ops",
        "runtime/array_ops",
        "runtime/file_ops",
        "runtime/error_ops"
    ]
    
    # Resolve core modules
    set resolved_core = resolve_files(core_modules)
    if resolved_core == null:
        say "Failed to resolve core modules"
        return null
    end
    
    # Resolve runtime modules
    set resolved_runtime = resolve_files(runtime_modules)
    if resolved_runtime == null:
        say "Failed to resolve runtime modules"
        return null
    end
    
    # Combine all resolved files
    set all_resolved = resolved_core + resolved_runtime
    set resolved_files = all_resolved
    
    say "Successfully resolved " + array_length(all_resolved) + " files"
    return all_resolved
end

# Get dependency order for files
fun get_file_dependency_order():
    # Define the correct order based on dependencies
    set ordered_files = [
        "src/error_handling.dolet",
        "src/memory_pool.dolet",
        "src/runtime/string_ops.dolet",
        "src/runtime/math_ops.dolet",
        "src/runtime/array_ops.dolet",
        "src/runtime/file_ops.dolet",
        "src/runtime/error_ops.dolet",
        "src/tokenizer.dolet",
        "src/symbol_table.dolet",
        "src/parser.dolet",
        "src/type_inference.dolet",
        "src/semantic_analyzer.dolet",
        "src/codegen.dolet",
        "src/module_loader.dolet",
        "src/file_resolver.dolet",
        "src/main.dolet"
    ]
    
    return ordered_files
end

# Validate all required files exist
fun validate_required_files():
    say "Validating required files..."
    
    set required_files = get_file_dependency_order()
    set missing_files = []
    
    set i = 0
    while i < array_length(required_files):
        set file_path = required_files[i]
        if !file_exists(file_path):
            set missing_files = missing_files + [file_path]
        end
        set i = i + 1
    end
    
    if array_length(missing_files) > 0:
        say "Missing required files:"
        set i = 0
        while i < array_length(missing_files):
            say "  - " + missing_files[i]
            set i = i + 1
        end
        return false
    end
    
    say "All required files found"
    return true
end

# Get resolved files list
fun get_resolved_files():
    return resolved_files
end

# Clear file cache
fun clear_file_cache():
    set file_cache = []
end

# Print file resolution info
fun print_resolution_info():
    say ""
    say "File Resolution Information:"
    say "==========================="
    say "Base paths:"
    set i = 0
    while i < array_length(base_paths):
        say "  - " + base_paths[i]
        set i = i + 1
    end
    
    say ""
    say "Resolved files:"
    set i = 0
    while i < array_length(resolved_files):
        say "  " + (i + 1) + ". " + resolved_files[i]
        set i = i + 1
    end
    
    say ""
    say "Cached files: " + array_length(file_cache)
end

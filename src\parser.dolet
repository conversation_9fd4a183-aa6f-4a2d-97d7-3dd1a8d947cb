# Dolet Self-Hosting Parser
# Recursive descent parser that builds Abstract Syntax Tree (AST) from tokens
# Implements the complete Dolet language grammar

# Import tokenizer constants
# TOKEN_* constants are defined in tokenizer.dolet

# AST Node Types
const AST_PROGRAM = 1
const AST_VAR_DECL = 2
const AST_FUN_DECL = 3
const AST_IF_STMT = 4
const AST_WHILE_STMT = 5
const AST_RETURN_STMT = 6
const AST_EXPRESSION_STMT = 7
const AST_SAY_STMT = 8
const AST_COMPOUND_ASSIGN = 9

# Expression AST Node Types
const AST_BINARY_EXPR = 20
const AST_UNARY_EXPR = 21
const AST_LITERAL_EXPR = 22
const AST_IDENTIFIER_EXPR = 23
const AST_CALL_EXPR = 24
const AST_ASSIGN_EXPR = 25
const AST_ARRAY_EXPR = 26

# Binary Operators
const OP_ADD = 1
const OP_SUBTRACT = 2
const OP_MULTIPLY = 3
const OP_DIVIDE = 4
const OP_MODULO = 5
const OP_EQUAL = 6
const OP_NOT_EQUAL = 7
const OP_LESS = 8
const OP_LESS_EQUAL = 9
const OP_GREATER = 10
const OP_GREATER_EQUAL = 11
const OP_AND = 12
const OP_OR = 13

# Unary Operators
const OP_NEGATE = 1
const OP_NOT = 2

# Parser state
set tokens = []
set current = 0
set had_error = false

# Initialize parser with tokens
fun init_parser(token_list):
    set tokens = token_list
    set current = 0
    set had_error = false
end

# Check if we're at end of tokens
fun is_at_end():
    return current >= array_length(tokens) || get_token_type(tokens[current]) == TOKEN_EOF
end

# Get current token
fun peek():
    if is_at_end():
        return create_token(TOKEN_EOF, "", 0, 0)
    end
    return tokens[current]
end

# Get previous token
fun previous():
    if current == 0:
        return create_token(TOKEN_EOF, "", 0, 0)
    end
    return tokens[current - 1]
end

# Advance to next token
fun advance():
    if !is_at_end():
        set current = current + 1
    end
    return previous()
end

# Check if current token matches type
fun check(token_type):
    if is_at_end():
        return false
    end
    return get_token_type(peek()) == token_type
end

# Consume token if it matches type
fun match(token_type):
    if check(token_type):
        advance()
        return true
    end
    return false
end

# Consume token or report error
fun consume(token_type, message):
    if check(token_type):
        return advance()
    end
    
    set token = peek()
    say "Parse Error at line " + get_token_line(token) + ": " + message
    set had_error = true
    return token
end

# Skip newlines
fun skip_newlines():
    while match(TOKEN_NEWLINE):
        # Continue skipping
    end
end

# Create AST node
fun create_ast_node(node_type, data):
    return [node_type, data]
end

fun get_ast_type(node):
    return node[0]
end

fun get_ast_data(node):
    return node[1]
end

# Parse the entire program
fun parse():
    set statements = []
    
    while !is_at_end():
        skip_newlines()
        if is_at_end():
            return create_ast_node(AST_PROGRAM, statements)
        end
        
        set stmt = parse_statement()
        if stmt != null:
            set statements = statements + [stmt]
        end
    end
    
    return create_ast_node(AST_PROGRAM, statements)
end

# Parse a statement
fun parse_statement():
    skip_newlines()
    
    if match(TOKEN_SET):
        return parse_var_declaration(false)
    end
    if match(TOKEN_CONST):
        return parse_var_declaration(true)
    end
    if match(TOKEN_FUN):
        return parse_function_declaration()
    end
    if match(TOKEN_IF):
        return parse_if_statement()
    end
    if match(TOKEN_WHILE):
        return parse_while_statement()
    end
    if match(TOKEN_RETURN):
        return parse_return_statement()
    end
    if match(TOKEN_SAY):
        return parse_say_statement()
    end
    
    return parse_expression_statement()
end

# Parse variable declaration
fun parse_var_declaration(is_const):
    set name_token = consume(TOKEN_IDENTIFIER, "Expected variable name")
    set name = get_token_lexeme(name_token)
    
    set type_annotation = null
    if match(TOKEN_COLON):
        # Type annotation (simplified - just store the type name)
        set type_token = advance()
        set type_annotation = get_token_lexeme(type_token)
    end
    
    set initializer = null
    if match(TOKEN_ASSIGN):
        set initializer = parse_expression()
    end
    
    skip_newlines()
    
    set data = [name, type_annotation, initializer, is_const]
    return create_ast_node(AST_VAR_DECL, data)
end

# Parse function declaration
fun parse_function_declaration():
    set name_token = consume(TOKEN_IDENTIFIER, "Expected function name")
    set name = get_token_lexeme(name_token)
    
    consume(TOKEN_LEFT_PAREN, "Expected '(' after function name")
    
    set parameters = []
    if !check(TOKEN_RIGHT_PAREN):
        set param_token = consume(TOKEN_IDENTIFIER, "Expected parameter name")
        set param_name = get_token_lexeme(param_token)
        
        set param_type = null
        if match(TOKEN_COLON):
            set type_token = advance()
            set param_type = get_token_lexeme(type_token)
        end
        
        set parameters = parameters + [[param_name, param_type]]
        
        while match(TOKEN_COMMA):
            set param_token = consume(TOKEN_IDENTIFIER, "Expected parameter name")
            set param_name = get_token_lexeme(param_token)
            
            set param_type = null
            if match(TOKEN_COLON):
                set type_token = advance()
                set param_type = get_token_lexeme(type_token)
            end
            
            set parameters = parameters + [[param_name, param_type]]
        end
    end
    
    consume(TOKEN_RIGHT_PAREN, "Expected ')' after parameters")
    
    set return_type = null
    if match(TOKEN_COLON):
        set type_token = advance()
        set return_type = get_token_lexeme(type_token)
    end
    
    skip_newlines()
    
    set body = []
    while !check(TOKEN_END) && !is_at_end():
        set stmt = parse_statement()
        if stmt != null:
            set body = body + [stmt]
        end
    end
    
    consume(TOKEN_END, "Expected 'end' after function body")
    skip_newlines()
    
    set data = [name, parameters, return_type, body]
    return create_ast_node(AST_FUN_DECL, data)
end

# Parse if statement
fun parse_if_statement():
    set condition = parse_expression()
    consume(TOKEN_COLON, "Expected ':' after if condition")
    skip_newlines()
    
    set then_branch = []
    while !check(TOKEN_ELSE) && !check(TOKEN_END) && !is_at_end():
        set stmt = parse_statement()
        if stmt != null:
            set then_branch = then_branch + [stmt]
        end
    end
    
    set else_branch = null
    if match(TOKEN_ELSE):
        consume(TOKEN_COLON, "Expected ':' after else")
        skip_newlines()
        
        set else_branch = []
        while !check(TOKEN_END) && !is_at_end():
            set stmt = parse_statement()
            if stmt != null:
                set else_branch = else_branch + [stmt]
            end
        end
    end
    
    consume(TOKEN_END, "Expected 'end' after if statement")
    skip_newlines()
    
    set data = [condition, then_branch, else_branch]
    return create_ast_node(AST_IF_STMT, data)
end

# Parse while statement
fun parse_while_statement():
    set condition = parse_expression()
    
    # Handle both syntaxes: "while condition:" and "while condition do"
    if match(TOKEN_DO):
        skip_newlines()
    else:
        consume(TOKEN_COLON, "Expected ':' or 'do' after while condition")
        skip_newlines()
    end
    
    set body = []
    while !check(TOKEN_END) && !is_at_end():
        set stmt = parse_statement()
        if stmt != null:
            set body = body + [stmt]
        end
    end
    
    consume(TOKEN_END, "Expected 'end' after while body")
    skip_newlines()
    
    set data = [condition, body]
    return create_ast_node(AST_WHILE_STMT, data)
end

# Parse return statement
fun parse_return_statement():
    set value = null
    if !check(TOKEN_NEWLINE) && !is_at_end():
        set value = parse_expression()
    end
    
    skip_newlines()
    
    set data = [value]
    return create_ast_node(AST_RETURN_STMT, data)
end

# Parse say statement
fun parse_say_statement():
    set expression = parse_expression()
    skip_newlines()
    
    set data = [expression]
    return create_ast_node(AST_SAY_STMT, data)
end

# Parse expression statement
fun parse_expression_statement():
    set expr = parse_expression()
    skip_newlines()

    set data = [expr]
    return create_ast_node(AST_EXPRESSION_STMT, data)
end

# Parse expression (assignment level)
fun parse_expression():
    return parse_assignment()
end

# Parse assignment
fun parse_assignment():
    set expr = parse_logical_or()

    if match(TOKEN_ASSIGN):
        set value = parse_assignment()
        set data = [expr, value]
        return create_ast_node(AST_ASSIGN_EXPR, data)
    end

    # Compound assignment operators
    if match(TOKEN_PLUS_ASSIGN):
        set value = parse_assignment()
        set data = [expr, OP_ADD, value]
        return create_ast_node(AST_COMPOUND_ASSIGN, data)
    end
    if match(TOKEN_MINUS_ASSIGN):
        set value = parse_assignment()
        set data = [expr, OP_SUBTRACT, value]
        return create_ast_node(AST_COMPOUND_ASSIGN, data)
    end
    if match(TOKEN_STAR_ASSIGN):
        set value = parse_assignment()
        set data = [expr, OP_MULTIPLY, value]
        return create_ast_node(AST_COMPOUND_ASSIGN, data)
    end
    if match(TOKEN_SLASH_ASSIGN):
        set value = parse_assignment()
        set data = [expr, OP_DIVIDE, value]
        return create_ast_node(AST_COMPOUND_ASSIGN, data)
    end

    return expr
end

# Parse logical OR
fun parse_logical_or():
    set expr = parse_logical_and()

    while match(TOKEN_OR):
        set operator = OP_OR
        set right = parse_logical_and()
        set data = [expr, operator, right]
        set expr = create_ast_node(AST_BINARY_EXPR, data)
    end

    return expr
end

# Parse logical AND
fun parse_logical_and():
    set expr = parse_equality()

    while match(TOKEN_AND):
        set operator = OP_AND
        set right = parse_equality()
        set data = [expr, operator, right]
        set expr = create_ast_node(AST_BINARY_EXPR, data)
    end

    return expr
end

# Parse equality
fun parse_equality():
    set expr = parse_comparison()

    while match(TOKEN_EQUAL_EQUAL) || match(TOKEN_BANG_EQUAL):
        set operator = OP_EQUAL
        if get_token_type(previous()) == TOKEN_BANG_EQUAL:
            set operator = OP_NOT_EQUAL
        end
        set right = parse_comparison()
        set data = [expr, operator, right]
        set expr = create_ast_node(AST_BINARY_EXPR, data)
    end

    return expr
end

# Parse comparison
fun parse_comparison():
    set expr = parse_term()

    while match(TOKEN_GREATER) || match(TOKEN_GREATER_EQUAL) || match(TOKEN_LESS) || match(TOKEN_LESS_EQUAL):
        set operator = OP_GREATER
        set prev_type = get_token_type(previous())
        if prev_type == TOKEN_GREATER_EQUAL:
            set operator = OP_GREATER_EQUAL
        else if prev_type == TOKEN_LESS:
            set operator = OP_LESS
        else if prev_type == TOKEN_LESS_EQUAL:
            set operator = OP_LESS_EQUAL
        end

        set right = parse_term()
        set data = [expr, operator, right]
        set expr = create_ast_node(AST_BINARY_EXPR, data)
    end

    return expr
end

# Parse term (addition and subtraction)
fun parse_term():
    set expr = parse_factor()

    while match(TOKEN_PLUS) || match(TOKEN_MINUS):
        set operator = OP_ADD
        if get_token_type(previous()) == TOKEN_MINUS:
            set operator = OP_SUBTRACT
        end
        set right = parse_factor()
        set data = [expr, operator, right]
        set expr = create_ast_node(AST_BINARY_EXPR, data)
    end

    return expr
end

# Parse factor (multiplication, division, modulo)
fun parse_factor():
    set expr = parse_unary()

    while match(TOKEN_STAR) || match(TOKEN_SLASH) || match(TOKEN_PERCENT):
        set operator = OP_MULTIPLY
        set prev_type = get_token_type(previous())
        if prev_type == TOKEN_SLASH:
            set operator = OP_DIVIDE
        else if prev_type == TOKEN_PERCENT:
            set operator = OP_MODULO
        end

        set right = parse_unary()
        set data = [expr, operator, right]
        set expr = create_ast_node(AST_BINARY_EXPR, data)
    end

    return expr
end

# Parse unary expressions
fun parse_unary():
    if match(TOKEN_NOT) || match(TOKEN_MINUS):
        set operator = OP_NOT
        if get_token_type(previous()) == TOKEN_MINUS:
            set operator = OP_NEGATE
        end
        set right = parse_unary()
        set data = [operator, right]
        return create_ast_node(AST_UNARY_EXPR, data)
    end

    return parse_call()
end

# Parse function calls
fun parse_call():
    set expr = parse_primary()

    while true:
        if match(TOKEN_LEFT_PAREN):
            set expr = finish_call(expr)
        else:
            return expr
        end
    end

    return expr
end

# Finish parsing function call
fun finish_call(callee):
    set arguments = []

    if !check(TOKEN_RIGHT_PAREN):
        set arguments = arguments + [parse_expression()]
        while match(TOKEN_COMMA):
            set arguments = arguments + [parse_expression()]
        end
    end

    consume(TOKEN_RIGHT_PAREN, "Expected ')' after arguments")

    set data = [callee, arguments]
    return create_ast_node(AST_CALL_EXPR, data)
end

# Parse primary expressions
fun parse_primary():
    if match(TOKEN_TRUE):
        set data = [true]
        return create_ast_node(AST_LITERAL_EXPR, data)
    end

    if match(TOKEN_FALSE):
        set data = [false]
        return create_ast_node(AST_LITERAL_EXPR, data)
    end

    if check(TOKEN_INTEGER):
        set token = advance()
        set value = string_to_int(get_token_lexeme(token))
        set data = [value]
        return create_ast_node(AST_LITERAL_EXPR, data)
    end

    if check(TOKEN_FLOAT) || check(TOKEN_DOUBLE):
        set token = advance()
        set value = string_to_float(get_token_lexeme(token))
        set data = [value]
        return create_ast_node(AST_LITERAL_EXPR, data)
    end

    if check(TOKEN_STRING):
        set token = advance()
        set value = get_token_lexeme(token)
        set data = [value]
        return create_ast_node(AST_LITERAL_EXPR, data)
    end

    if check(TOKEN_CHAR):
        set token = advance()
        set value = get_token_lexeme(token)
        set data = [value]
        return create_ast_node(AST_LITERAL_EXPR, data)
    end

    if check(TOKEN_IDENTIFIER):
        set token = advance()
        set name = get_token_lexeme(token)
        set data = [name]
        return create_ast_node(AST_IDENTIFIER_EXPR, data)
    end

    if match(TOKEN_LEFT_PAREN):
        set expr = parse_expression()
        consume(TOKEN_RIGHT_PAREN, "Expected ')' after expression")
        return expr
    end

    if match(TOKEN_LEFT_BRACKET):
        return parse_array_literal()
    end

    set token = peek()
    say "Parse Error at line " + get_token_line(token) + ": Unexpected token '" + get_token_lexeme(token) + "'"
    set had_error = true
    return null
end

# Parse array literal
fun parse_array_literal():
    set elements = []

    if !check(TOKEN_RIGHT_BRACKET):
        set elements = elements + [parse_expression()]
        while match(TOKEN_COMMA):
            set elements = elements + [parse_expression()]
        end
    end

    consume(TOKEN_RIGHT_BRACKET, "Expected ']' after array elements")

    set data = [elements]
    return create_ast_node(AST_ARRAY_EXPR, data)
end

# Main parse function
fun parse_program(token_list):
    init_parser(token_list)
    set ast = parse()

    if had_error:
        return null
    end

    return ast
end

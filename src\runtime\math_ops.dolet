# Dolet Runtime - Math Operations
# High-performance mathematical functions for the Dolet runtime

# Absolute value for integers
fun abs_int(x):
    if x < 0:
        return -x
    end
    return x
end

# Absolute value for floats
fun abs_float(x):
    if x < 0.0:
        return -x
    end
    return x
end

# Maximum of two integers
fun max_int(a, b):
    if a > b:
        return a
    end
    return b
end

# Minimum of two integers
fun min_int(a, b):
    if a < b:
        return a
    end
    return b
end

# Maximum of two floats
fun max_float(a, b):
    if a > b:
        return a
    end
    return b
end

# Minimum of two floats
fun min_float(a, b):
    if a < b:
        return a
    end
    return b
end

# Power function (integer exponent)
fun pow_int(base, exponent):
    if exponent == 0:
        return 1
    end
    if exponent < 0:
        return 0  # Integer division would result in 0 for negative exponents
    end
    
    set result = 1
    set i = 0
    while i < exponent:
        set result = result * base
        set i = i + 1
    end
    return result
end

# Power function (float base, integer exponent)
fun pow_float(base, exponent):
    if exponent == 0:
        return 1.0
    end
    if exponent < 0:
        set result = 1.0
        set i = 0
        while i < -exponent:
            set result = result / base
            set i = i + 1
        end
        return result
    end
    
    set result = 1.0
    set i = 0
    while i < exponent:
        set result = result * base
        set i = i + 1
    end
    return result
end

# Square root approximation using Newton's method
fun sqrt_float(x):
    if x < 0.0:
        return 0.0  # Invalid input
    end
    if x == 0.0:
        return 0.0
    end
    if x == 1.0:
        return 1.0
    end
    
    set guess = x / 2.0
    set precision = 0.000001
    set iterations = 0
    set max_iterations = 50
    
    while iterations < max_iterations:
        set new_guess = (guess + x / guess) / 2.0
        if abs_float(new_guess - guess) < precision:
            return new_guess
        end
        set guess = new_guess
        set iterations = iterations + 1
    end
    
    return guess
end

# Factorial function
fun factorial(n):
    if n < 0:
        return 0  # Invalid input
    end
    if n == 0 || n == 1:
        return 1
    end
    
    set result = 1
    set i = 2
    while i <= n:
        set result = result * i
        set i = i + 1
    end
    return result
end

# Greatest Common Divisor (GCD)
fun gcd(a, b):
    set a = abs_int(a)
    set b = abs_int(b)
    
    while b != 0:
        set temp = b
        set b = a % b
        set a = temp
    end
    return a
end

# Least Common Multiple (LCM)
fun lcm(a, b):
    if a == 0 || b == 0:
        return 0
    end
    return abs_int(a * b) / gcd(a, b)
end

# Check if number is prime
fun is_prime(n):
    if n < 2:
        return false
    end
    if n == 2:
        return true
    end
    if n % 2 == 0:
        return false
    end
    
    set i = 3
    while i * i <= n:
        if n % i == 0:
            return false
        end
        set i = i + 2
    end
    return true
end

# Fibonacci number
fun fibonacci(n):
    if n < 0:
        return 0
    end
    if n == 0:
        return 0
    end
    if n == 1:
        return 1
    end
    
    set a = 0
    set b = 1
    set i = 2
    while i <= n:
        set temp = a + b
        set a = b
        set b = temp
        set i = i + 1
    end
    return b
end

# Clamp value between min and max
fun clamp_int(value, min_val, max_val):
    if value < min_val:
        return min_val
    end
    if value > max_val:
        return max_val
    end
    return value
end

fun clamp_float(value, min_val, max_val):
    if value < min_val:
        return min_val
    end
    if value > max_val:
        return max_val
    end
    return value
end

# Linear interpolation
fun lerp(a, b, t):
    return a + (b - a) * t
end

# Sign function
fun sign_int(x):
    if x > 0:
        return 1
    end
    if x < 0:
        return -1
    end
    return 0
end

fun sign_float(x):
    if x > 0.0:
        return 1.0
    end
    if x < 0.0:
        return -1.0
    end
    return 0.0
end

# Floor function (integer part of float)
fun floor_float(x):
    set int_part = x
    if x < 0.0 && x != int_part:
        return int_part - 1
    end
    return int_part
end

# Ceiling function
fun ceil_float(x):
    set int_part = x
    if x > 0.0 && x != int_part:
        return int_part + 1
    end
    return int_part
end

# Round to nearest integer
fun round_float(x):
    if x >= 0.0:
        return floor_float(x + 0.5)
    else:
        return ceil_float(x - 0.5)
    end
end

# Modulo for floats
fun fmod(x, y):
    if y == 0.0:
        return 0.0
    end
    set quotient = x / y
    set int_quotient = floor_float(quotient)
    return x - int_quotient * y
end

# Simple trigonometric approximations (using Taylor series)
# Sine approximation (input in radians)
fun sin_approx(x):
    # Normalize to [-π, π] range (approximately [-3.14159, 3.14159])
    while x > 3.14159:
        set x = x - 6.28318
    end
    while x < -3.14159:
        set x = x + 6.28318
    end
    
    # Taylor series: sin(x) = x - x³/3! + x⁵/5! - x⁷/7! + ...
    set result = x
    set term = x
    set i = 1
    
    while i < 10:  # 10 terms should be sufficient for reasonable accuracy
        set term = term * (-x * x) / ((2 * i) * (2 * i + 1))
        set result = result + term
        set i = i + 1
    end
    
    return result
end

# Cosine approximation
fun cos_approx(x):
    # cos(x) = sin(x + π/2)
    return sin_approx(x + 1.5708)
end

# Tangent approximation
fun tan_approx(x):
    set cos_val = cos_approx(x)
    if abs_float(cos_val) < 0.000001:
        return 0.0  # Avoid division by zero
    end
    return sin_approx(x) / cos_val
end

# Natural logarithm approximation (for x > 0)
fun ln_approx(x):
    if x <= 0.0:
        return 0.0  # Invalid input
    end
    if x == 1.0:
        return 0.0
    end
    
    # Use the series: ln(1+u) = u - u²/2 + u³/3 - u⁴/4 + ...
    # Transform x to 1+u form
    set u = x - 1.0
    
    # For better convergence, use ln(x) = 2 * ln(sqrt(x))
    if abs_float(u) > 0.5:
        return 2.0 * ln_approx(sqrt_float(x))
    end
    
    set result = u
    set term = u
    set i = 2
    
    while i <= 20:
        set term = term * (-u)
        set result = result + term / i
        set i = i + 1
    end
    
    return result
end

# Exponential function approximation
fun exp_approx(x):
    if x == 0.0:
        return 1.0
    end
    
    # Taylor series: e^x = 1 + x + x²/2! + x³/3! + ...
    set result = 1.0
    set term = 1.0
    set i = 1
    
    while i <= 20:
        set term = term * x / i
        set result = result + term
        set i = i + 1
    end
    
    return result
end

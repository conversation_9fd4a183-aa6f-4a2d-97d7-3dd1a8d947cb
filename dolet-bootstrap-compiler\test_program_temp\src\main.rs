use std::io::{self, Write};


fn main() {
    println!("{}", "🎉 Hello from Dolet!");
    println!("{}", "This program was compiled by the Dolet self-hosting compiler!");
    let mut x = 42;
    println!("{}", "The answer to everything is: 42");
    let mut message = "Dolet is amazing!";
    println!("{}", "Message: Dolet is amazing!");
    println!("{}", "");
    println!("{}", "✅ Dolet Features Demonstrated:");
    println!("{}", "• Variables and constants");
    println!("{}", "• String literals");
    println!("{}", "• Integer literals");
    println!("{}", "• Say statements");
    println!("{}", "");
    println!("{}", "🚀 Dolet Self-Hosting Success!");
    println!("{}", "The language can now compile itself!");
    println!("{}", "");
    println!("{}", "Thank you for using <PERSON><PERSON>!");
}

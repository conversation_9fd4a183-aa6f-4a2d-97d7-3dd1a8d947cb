# Dolet Module Loader
# يحمل ويدير ملفات .dolet متعددة لبناء مترجم موحد

# Module information structure
# [module_name, file_path, source_code, dependencies, is_loaded]
set loaded_modules = []
set module_dependencies = []
set loading_order = []

# Initialize module loader
fun init_module_loader():
    set loaded_modules = []
    set module_dependencies = []
    set loading_order = []
end

# Create module info
fun create_module_info(name, file_path, source_code):
    return [name, file_path, source_code, [], false]
end

# Get module properties
fun get_module_name(module):
    return module[0]
end

fun get_module_path(module):
    return module[1]
end

fun get_module_source(module):
    return module[2]
end

fun get_module_dependencies(module):
    return module[3]
end

fun is_module_loaded(module):
    return module[4]
end

fun set_module_loaded(module, loaded):
    set module[4] = loaded
end

# Add dependency to module
fun add_module_dependency(module, dependency_name):
    set deps = get_module_dependencies(module)
    set module[3] = deps + [dependency_name]
end

# Load single module file
fun load_module_file(file_path):
    # في التطبيق الحقيقي، هذا سيقرأ من نظام الملفات
    # الآن سنحاكي قراءة الملفات
    
    if file_path == "src/tokenizer.dolet":
        return "# Tokenizer module content..."
    end
    if file_path == "src/parser.dolet":
        return "# Parser module content..."
    end
    if file_path == "src/symbol_table.dolet":
        return "# Symbol table module content..."
    end
    if file_path == "src/type_inference.dolet":
        return "# Type inference module content..."
    end
    if file_path == "src/semantic_analyzer.dolet":
        return "# Semantic analyzer module content..."
    end
    if file_path == "src/error_handling.dolet":
        return "# Error handling module content..."
    end
    if file_path == "src/memory_pool.dolet":
        return "# Memory pool module content..."
    end
    if file_path == "src/codegen.dolet":
        return "# Code generator module content..."
    end
    if file_path == "src/main.dolet":
        return "# Main compiler module content..."
    end
    
    # Runtime modules
    if file_path == "src/runtime/string_ops.dolet":
        return "# String operations runtime..."
    end
    if file_path == "src/runtime/math_ops.dolet":
        return "# Math operations runtime..."
    end
    if file_path == "src/runtime/array_ops.dolet":
        return "# Array operations runtime..."
    end
    if file_path == "src/runtime/file_ops.dolet":
        return "# File operations runtime..."
    end
    if file_path == "src/runtime/error_ops.dolet":
        return "# Error operations runtime..."
    end
    
    return null  # File not found
end

# Extract module name from file path
fun extract_module_name(file_path):
    # Extract filename without extension
    set last_slash = -1
    set last_dot = -1
    
    set i = 0
    while i < length(file_path):
        if file_path[i] == '/' || file_path[i] == '\\':
            set last_slash = i
        end
        if file_path[i] == '.':
            set last_dot = i
        end
        set i = i + 1
    end
    
    set start = last_slash + 1
    set end_pos = last_dot
    if end_pos == -1:
        set end_pos = length(file_path)
    end
    
    return substring(file_path, start, end_pos - start)
end

# Load module by path
fun load_module(file_path):
    # Check if already loaded
    set i = 0
    while i < array_length(loaded_modules):
        set module = loaded_modules[i]
        if get_module_path(module) == file_path:
            return module  # Already loaded
        end
        set i = i + 1
    end
    
    # Load source code
    set source_code = load_module_file(file_path)
    if source_code == null:
        say "Error: Could not load module: " + file_path
        return null
    end
    
    # Extract module name
    set module_name = extract_module_name(file_path)
    
    # Create module info
    set module = create_module_info(module_name, file_path, source_code)
    
    # Add to loaded modules
    set loaded_modules = loaded_modules + [module]
    
    say "Loaded module: " + module_name + " from " + file_path
    return module
end

# Load all compiler modules
fun load_all_compiler_modules():
    say "Loading Dolet compiler modules..."
    
    # Core compiler modules
    set core_modules = [
        "src/tokenizer.dolet",
        "src/parser.dolet", 
        "src/symbol_table.dolet",
        "src/type_inference.dolet",
        "src/semantic_analyzer.dolet",
        "src/error_handling.dolet",
        "src/memory_pool.dolet",
        "src/codegen.dolet",
        "src/main.dolet"
    ]
    
    # Runtime modules
    set runtime_modules = [
        "src/runtime/string_ops.dolet",
        "src/runtime/math_ops.dolet",
        "src/runtime/array_ops.dolet",
        "src/runtime/file_ops.dolet",
        "src/runtime/error_ops.dolet"
    ]
    
    # Load core modules
    set i = 0
    while i < array_length(core_modules):
        set module = load_module(core_modules[i])
        if module == null:
            say "Failed to load core module: " + core_modules[i]
            return false
        end
        set i = i + 1
    end
    
    # Load runtime modules
    set i = 0
    while i < array_length(runtime_modules):
        set module = load_module(runtime_modules[i])
        if module == null:
            say "Failed to load runtime module: " + runtime_modules[i]
            return false
        end
        set i = i + 1
    end
    
    say "Successfully loaded " + array_length(loaded_modules) + " modules"
    return true
end

# Analyze module dependencies
fun analyze_dependencies():
    say "Analyzing module dependencies..."
    
    # Define dependency relationships
    # tokenizer -> (no dependencies)
    # parser -> tokenizer
    # symbol_table -> (no dependencies)
    # type_inference -> symbol_table, parser
    # semantic_analyzer -> symbol_table, type_inference, parser
    # error_handling -> (no dependencies)
    # memory_pool -> (no dependencies)
    # codegen -> parser, symbol_table, type_inference
    # main -> all modules
    
    set i = 0
    while i < array_length(loaded_modules):
        set module = loaded_modules[i]
        set name = get_module_name(module)
        
        if name == "parser":
            add_module_dependency(module, "tokenizer")
        else if name == "type_inference":
            add_module_dependency(module, "symbol_table")
            add_module_dependency(module, "parser")
        else if name == "semantic_analyzer":
            add_module_dependency(module, "symbol_table")
            add_module_dependency(module, "type_inference")
            add_module_dependency(module, "parser")
        else if name == "codegen":
            add_module_dependency(module, "parser")
            add_module_dependency(module, "symbol_table")
            add_module_dependency(module, "type_inference")
        else if name == "main":
            add_module_dependency(module, "tokenizer")
            add_module_dependency(module, "parser")
            add_module_dependency(module, "symbol_table")
            add_module_dependency(module, "type_inference")
            add_module_dependency(module, "semantic_analyzer")
            add_module_dependency(module, "error_handling")
            add_module_dependency(module, "memory_pool")
            add_module_dependency(module, "codegen")
        end
        
        set i = i + 1
    end
    
    say "Dependency analysis complete"
    return true
end

# Calculate loading order based on dependencies
fun calculate_loading_order():
    say "Calculating module loading order..."
    
    set loading_order = []
    set processed = []
    
    # Add modules with no dependencies first
    set i = 0
    while i < array_length(loaded_modules):
        set module = loaded_modules[i]
        set deps = get_module_dependencies(module)
        if array_length(deps) == 0:
            set loading_order = loading_order + [get_module_name(module)]
            set processed = processed + [get_module_name(module)]
        end
        set i = i + 1
    end
    
    # Add modules based on dependency resolution
    set changed = true
    while changed:
        set changed = false
        set i = 0
        while i < array_length(loaded_modules):
            set module = loaded_modules[i]
            set name = get_module_name(module)
            
            # Skip if already processed
            set already_processed = false
            set j = 0
            while j < array_length(processed):
                if processed[j] == name:
                    set already_processed = true
                end
                set j = j + 1
            end
            
            if !already_processed:
                # Check if all dependencies are processed
                set deps = get_module_dependencies(module)
                set all_deps_ready = true
                
                set k = 0
                while k < array_length(deps):
                    set dep_ready = false
                    set l = 0
                    while l < array_length(processed):
                        if processed[l] == deps[k]:
                            set dep_ready = true
                        end
                        set l = l + 1
                    end
                    if !dep_ready:
                        set all_deps_ready = false
                    end
                    set k = k + 1
                end
                
                if all_deps_ready:
                    set loading_order = loading_order + [name]
                    set processed = processed + [name]
                    set changed = true
                end
            end
            
            set i = i + 1
        end
    end
    
    say "Loading order calculated: " + array_length(loading_order) + " modules"
    return true
end

# Get module by name
fun get_module_by_name(name):
    set i = 0
    while i < array_length(loaded_modules):
        set module = loaded_modules[i]
        if get_module_name(module) == name:
            return module
        end
        set i = i + 1
    end
    return null
end

# Print module information
fun print_module_info():
    say ""
    say "Loaded Modules:"
    say "==============="
    
    set i = 0
    while i < array_length(loaded_modules):
        set module = loaded_modules[i]
        set name = get_module_name(module)
        set path = get_module_path(module)
        set deps = get_module_dependencies(module)
        
        say "Module: " + name
        say "  Path: " + path
        say "  Dependencies: " + array_length(deps)
        
        set j = 0
        while j < array_length(deps):
            say "    - " + deps[j]
            set j = j + 1
        end
        say ""
        set i = i + 1
    end
    
    say "Loading Order:"
    say "=============="
    set i = 0
    while i < array_length(loading_order):
        say (i + 1) + ". " + loading_order[i]
        set i = i + 1
    end
end

# Get all loaded modules
fun get_loaded_modules():
    return loaded_modules
end

# Get loading order
fun get_loading_order():
    return loading_order
end

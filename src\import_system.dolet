# Dolet Import System
# يدير استيراد ودمج الوحدات في برنامج واحد

# Import state
set imported_modules = []
set import_graph = []
set circular_imports = []

# Initialize import system
fun init_import_system():
    set imported_modules = []
    set import_graph = []
    set circular_imports = []
end

# Import entry structure
# [module_name, file_path, exports, imports, processed]
fun create_import_entry(module_name, file_path):
    return [module_name, file_path, [], [], false]
end

# Get import entry properties
fun get_import_module_name(entry):
    return entry[0]
end

fun get_import_file_path(entry):
    return entry[1]
end

fun get_import_exports(entry):
    return entry[2]
end

fun get_import_imports(entry):
    return entry[3]
end

fun is_import_processed(entry):
    return entry[4]
end

fun set_import_processed(entry, processed):
    set entry[4] = processed
end

# Add export to module
fun add_export(entry, export_name, export_type):
    set exports = get_import_exports(entry)
    set export_info = [export_name, export_type]
    set entry[2] = exports + [export_info]
end

# Add import to module
fun add_import(entry, import_name, from_module):
    set imports = get_import_imports(entry)
    set import_info = [import_name, from_module]
    set entry[3] = imports + [import_info]
end

# Register module for import
fun register_module(module_name, file_path):
    # Check if already registered
    set i = 0
    while i < array_length(imported_modules):
        set entry = imported_modules[i]
        if get_import_module_name(entry) == module_name:
            return entry  # Already registered
        end
        set i = i + 1
    end
    
    # Create new import entry
    set entry = create_import_entry(module_name, file_path)
    set imported_modules = imported_modules + [entry]
    
    say "Registered module: " + module_name
    return entry
end

# Analyze module exports (simplified)
fun analyze_module_exports(module_name, source_code):
    set entry = get_module_entry(module_name)
    if entry == null:
        return false
    end
    
    # في التطبيق الحقيقي، هذا سيحلل الكود لاستخراج الدوال والمتغيرات المصدرة
    # الآن سنحدد الصادرات يدوياً بناءً على اسم الوحدة
    
    if module_name == "tokenizer":
        add_export(entry, "get_tokens", "function")
        add_export(entry, "create_token", "function")
        add_export(entry, "TOKEN_INTEGER", "constant")
        add_export(entry, "TOKEN_STRING", "constant")
        add_export(entry, "TOKEN_IDENTIFIER", "constant")
    else if module_name == "parser":
        add_export(entry, "parse_program", "function")
        add_export(entry, "create_ast_node", "function")
        add_export(entry, "AST_PROGRAM", "constant")
        add_export(entry, "AST_VAR_DECL", "constant")
    else if module_name == "symbol_table":
        add_export(entry, "init_symbol_table", "function")
        add_export(entry, "define_symbol", "function")
        add_export(entry, "lookup_symbol", "function")
        add_export(entry, "TYPE_INT", "constant")
        add_export(entry, "TYPE_STRING", "constant")
    else if module_name == "type_inference":
        add_export(entry, "infer_program_types", "function")
        add_export(entry, "infer_expression_type", "function")
    else if module_name == "semantic_analyzer":
        add_export(entry, "analyze_program", "function")
        add_export(entry, "analyze_expression", "function")
    else if module_name == "error_handling":
        add_export(entry, "init_error_handling", "function")
        add_export(entry, "add_error", "function")
        add_export(entry, "has_errors", "function")
        add_export(entry, "print_all_errors", "function")
    else if module_name == "memory_pool":
        add_export(entry, "init_memory_pools", "function")
        add_export(entry, "pool_allocate", "function")
        add_export(entry, "print_memory_stats", "function")
    else if module_name == "codegen":
        add_export(entry, "generate_code", "function")
        add_export(entry, "init_codegen", "function")
    else if module_name == "main":
        add_export(entry, "main_compile", "function")
        add_export(entry, "compiler_main", "function")
    end
    
    # Runtime modules
    if module_name == "string_ops":
        add_export(entry, "length", "function")
        add_export(entry, "concat", "function")
        add_export(entry, "string_to_int", "function")
        add_export(entry, "int_to_string", "function")
    else if module_name == "math_ops":
        add_export(entry, "abs_int", "function")
        add_export(entry, "max_int", "function")
        add_export(entry, "min_int", "function")
        add_export(entry, "sqrt_float", "function")
    else if module_name == "array_ops":
        add_export(entry, "array_length", "function")
        add_export(entry, "array_get", "function")
        add_export(entry, "array_push", "function")
    else if module_name == "file_ops":
        add_export(entry, "file_read_all", "function")
        add_export(entry, "file_write_all", "function")
        add_export(entry, "file_exists", "function")
    else if module_name == "error_ops":
        add_export(entry, "throw_runtime_error", "function")
        add_export(entry, "safe_divide_int", "function")
        add_export(entry, "assert", "function")
    end
    
    return true
end

# Analyze module imports (simplified)
fun analyze_module_imports(module_name, source_code):
    set entry = get_module_entry(module_name)
    if entry == null:
        return false
    end
    
    # تحديد الاستيرادات بناءً على التبعيات المعروفة
    if module_name == "parser":
        add_import(entry, "get_tokens", "tokenizer")
        add_import(entry, "TOKEN_INTEGER", "tokenizer")
    else if module_name == "type_inference":
        add_import(entry, "lookup_symbol", "symbol_table")
        add_import(entry, "TYPE_INT", "symbol_table")
        add_import(entry, "parse_program", "parser")
    else if module_name == "semantic_analyzer":
        add_import(entry, "lookup_symbol", "symbol_table")
        add_import(entry, "infer_expression_type", "type_inference")
        add_import(entry, "parse_program", "parser")
    else if module_name == "codegen":
        add_import(entry, "parse_program", "parser")
        add_import(entry, "lookup_symbol", "symbol_table")
        add_import(entry, "infer_expression_type", "type_inference")
    else if module_name == "main":
        add_import(entry, "get_tokens", "tokenizer")
        add_import(entry, "parse_program", "parser")
        add_import(entry, "analyze_program", "semantic_analyzer")
        add_import(entry, "infer_program_types", "type_inference")
        add_import(entry, "generate_code", "codegen")
        add_import(entry, "init_error_handling", "error_handling")
        add_import(entry, "init_memory_pools", "memory_pool")
    end
    
    return true
end

# Get module entry by name
fun get_module_entry(module_name):
    set i = 0
    while i < array_length(imported_modules):
        set entry = imported_modules[i]
        if get_import_module_name(entry) == module_name:
            return entry
        end
        set i = i + 1
    end
    return null
end

# Check for circular imports
fun check_circular_imports():
    say "Checking for circular imports..."
    
    set circular_imports = []
    
    # Simple cycle detection
    set i = 0
    while i < array_length(imported_modules):
        set entry = imported_modules[i]
        set module_name = get_import_module_name(entry)
        set imports = get_import_imports(entry)
        
        # Check if any imported module imports this module back
        set j = 0
        while j < array_length(imports):
            set import_info = imports[j]
            set imported_module = import_info[1]
            
            set imported_entry = get_module_entry(imported_module)
            if imported_entry != null:
                set imported_imports = get_import_imports(imported_entry)
                
                set k = 0
                while k < array_length(imported_imports):
                    set imported_import = imported_imports[k]
                    if imported_import[1] == module_name:
                        set circular = [module_name, imported_module]
                        set circular_imports = circular_imports + [circular]
                    end
                    set k = k + 1
                end
            end
            set j = j + 1
        end
        set i = i + 1
    end
    
    if array_length(circular_imports) > 0:
        say "Warning: Circular imports detected:"
        set i = 0
        while i < array_length(circular_imports):
            set circular = circular_imports[i]
            say "  " + circular[0] + " <-> " + circular[1]
            set i = i + 1
        end
        return false
    end
    
    say "No circular imports found"
    return true
end

# Calculate import order
fun calculate_import_order():
    say "Calculating import order..."
    
    set import_order = []
    set processed = []
    
    # Add modules with no imports first
    set i = 0
    while i < array_length(imported_modules):
        set entry = imported_modules[i]
        set imports = get_import_imports(entry)
        if array_length(imports) == 0:
            set module_name = get_import_module_name(entry)
            set import_order = import_order + [module_name]
            set processed = processed + [module_name]
        end
        set i = i + 1
    end
    
    # Add modules based on dependency resolution
    set changed = true
    while changed:
        set changed = false
        set i = 0
        while i < array_length(imported_modules):
            set entry = imported_modules[i]
            set module_name = get_import_module_name(entry)
            
            # Skip if already processed
            set already_processed = false
            set j = 0
            while j < array_length(processed):
                if processed[j] == module_name:
                    set already_processed = true
                end
                set j = j + 1
            end
            
            if !already_processed:
                # Check if all imports are processed
                set imports = get_import_imports(entry)
                set all_imports_ready = true
                
                set k = 0
                while k < array_length(imports):
                    set import_info = imports[k]
                    set imported_module = import_info[1]
                    
                    set import_ready = false
                    set l = 0
                    while l < array_length(processed):
                        if processed[l] == imported_module:
                            set import_ready = true
                        end
                        set l = l + 1
                    end
                    if !import_ready:
                        set all_imports_ready = false
                    end
                    set k = k + 1
                end
                
                if all_imports_ready:
                    set import_order = import_order + [module_name]
                    set processed = processed + [module_name]
                    set changed = true
                end
            end
            
            set i = i + 1
        end
    end
    
    say "Import order calculated: " + array_length(import_order) + " modules"
    return import_order
end

# Process all imports
fun process_all_imports():
    say "Processing all imports..."
    
    # Register all modules first
    set module_files = [
        ["tokenizer", "src/tokenizer.dolet"],
        ["parser", "src/parser.dolet"],
        ["symbol_table", "src/symbol_table.dolet"],
        ["type_inference", "src/type_inference.dolet"],
        ["semantic_analyzer", "src/semantic_analyzer.dolet"],
        ["error_handling", "src/error_handling.dolet"],
        ["memory_pool", "src/memory_pool.dolet"],
        ["codegen", "src/codegen.dolet"],
        ["main", "src/main.dolet"],
        ["string_ops", "src/runtime/string_ops.dolet"],
        ["math_ops", "src/runtime/math_ops.dolet"],
        ["array_ops", "src/runtime/array_ops.dolet"],
        ["file_ops", "src/runtime/file_ops.dolet"],
        ["error_ops", "src/runtime/error_ops.dolet"]
    ]
    
    # Register modules
    set i = 0
    while i < array_length(module_files):
        set module_info = module_files[i]
        register_module(module_info[0], module_info[1])
        set i = i + 1
    end
    
    # Analyze exports and imports
    set i = 0
    while i < array_length(module_files):
        set module_info = module_files[i]
        set module_name = module_info[0]
        analyze_module_exports(module_name, "")
        analyze_module_imports(module_name, "")
        set i = i + 1
    end
    
    # Check for circular imports
    set no_circular = check_circular_imports()
    if !no_circular:
        say "Warning: Circular imports detected, but continuing..."
    end
    
    # Calculate import order
    set order = calculate_import_order()
    
    say "Import processing complete"
    return order
end

# Print import information
fun print_import_info():
    say ""
    say "Import System Information:"
    say "========================="
    
    set i = 0
    while i < array_length(imported_modules):
        set entry = imported_modules[i]
        set module_name = get_import_module_name(entry)
        set exports = get_import_exports(entry)
        set imports = get_import_imports(entry)
        
        say "Module: " + module_name
        say "  Exports: " + array_length(exports)
        say "  Imports: " + array_length(imports)
        say ""
        set i = i + 1
    end
end

# Get imported modules
fun get_imported_modules():
    return imported_modules
end

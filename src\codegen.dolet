# Dolet Self-Hosting Code Generator
# Generates optimized machine code directly without intermediate representations
# Produces native executables for maximum performance

# Target architecture constants
const ARCH_X86_64 = 1
const ARCH_ARM64 = 2
const ARCH_X86 = 3

# Register allocation
const REG_RAX = 0
const REG_RBX = 1
const REG_RCX = 2
const REG_RDX = 3
const REG_RSI = 4
const REG_RDI = 5
const REG_RSP = 6
const REG_RBP = 7

# Code generation state
set target_arch = ARCH_X86_64
set code_buffer = []
set string_literals = []
set variable_offsets = []
set function_addresses = []
set current_stack_offset = 0
set register_usage = []

# Initialize code generator
fun init_codegen():
    set code_buffer = []
    set string_literals = []
    set variable_offsets = []
    set function_addresses = []
    set current_stack_offset = 0
    set register_usage = [false, false, false, false, false, false, false, false]
end

# Emit byte to code buffer
fun emit_byte(byte_val):
    set code_buffer = code_buffer + [byte_val]
end

# Emit 32-bit integer (little endian)
fun emit_int32(value):
    emit_byte(value % 256)
    emit_byte((value / 256) % 256)
    emit_byte((value / 65536) % 256)
    emit_byte((value / 16777216) % 256)
end

# Emit 64-bit integer (little endian)
fun emit_int64(value):
    # For simplicity, we'll emit as two 32-bit values
    emit_int32(value % 4294967296)
    emit_int32(value / 4294967296)
end

# Allocate register
fun allocate_register():
    set i = 0
    while i < array_length(register_usage):
        if !register_usage[i]:
            set register_usage[i] = true
            return i
        end
        set i = i + 1
    end
    return -1  # No free registers
end

# Free register
fun free_register(reg):
    if reg >= 0 && reg < array_length(register_usage):
        set register_usage[reg] = false
    end
end

# Get variable offset
fun get_variable_offset(name):
    set i = 0
    while i < array_length(variable_offsets):
        set entry = variable_offsets[i]
        if entry[0] == name:
            return entry[1]
        end
        set i = i + 1
    end
    return -1
end

# Add variable offset
fun add_variable_offset(name, offset):
    set variable_offsets = variable_offsets + [[name, offset]]
end

# Add string literal
fun add_string_literal(str):
    set index = array_length(string_literals)
    set string_literals = string_literals + [str]
    return index
end

# Generate x86-64 instruction: MOV reg, imm
fun emit_mov_reg_imm(reg, value):
    if reg == REG_RAX:
        emit_byte(0x48)  # REX.W prefix
        emit_byte(0xB8)  # MOV RAX, imm64
    else if reg == REG_RBX:
        emit_byte(0x48)
        emit_byte(0xBB)  # MOV RBX, imm64
    else if reg == REG_RCX:
        emit_byte(0x48)
        emit_byte(0xB9)  # MOV RCX, imm64
    else if reg == REG_RDX:
        emit_byte(0x48)
        emit_byte(0xBA)  # MOV RDX, imm64
    end
    emit_int64(value)
end

# Generate x86-64 instruction: ADD reg, reg
fun emit_add_reg_reg(dest_reg, src_reg):
    emit_byte(0x48)  # REX.W prefix
    emit_byte(0x01)  # ADD r/m64, r64
    # ModR/M byte: 11 (register mode) + src_reg * 8 + dest_reg
    emit_byte(192 + src_reg * 8 + dest_reg)
end

# Generate x86-64 instruction: SUB reg, reg
fun emit_sub_reg_reg(dest_reg, src_reg):
    emit_byte(0x48)  # REX.W prefix
    emit_byte(0x29)  # SUB r/m64, r64
    emit_byte(192 + src_reg * 8 + dest_reg)
end

# Generate x86-64 instruction: MUL reg
fun emit_mul_reg(reg):
    emit_byte(0x48)  # REX.W prefix
    emit_byte(0xF7)  # MUL r/m64
    emit_byte(224 + reg)  # ModR/M: 11100 + reg
end

# Generate x86-64 instruction: PUSH reg
fun emit_push_reg(reg):
    emit_byte(0x50 + reg)  # PUSH r64
end

# Generate x86-64 instruction: POP reg
fun emit_pop_reg(reg):
    emit_byte(0x58 + reg)  # POP r64
end

# Generate x86-64 instruction: RET
fun emit_ret():
    emit_byte(0xC3)  # RET
end

# Generate x86-64 instruction: CALL relative
fun emit_call_rel(offset):
    emit_byte(0xE8)  # CALL rel32
    emit_int32(offset)
end

# Generate system call (Linux x86-64)
fun emit_syscall():
    emit_byte(0x0F)  # SYSCALL
    emit_byte(0x05)
end

# Generate code for literal expression
fun generate_literal(expr):
    set ast_data = get_ast_data(expr)
    set value = ast_data[0]
    
    set reg = allocate_register()
    if reg == -1:
        add_codegen_error("No free registers available", 0, 0, "")
        return -1
    end
    
    # Check value type and generate appropriate code
    if value == true:
        emit_mov_reg_imm(reg, 1)
    else if value == false:
        emit_mov_reg_imm(reg, 0)
    else:
        # Assume integer for simplicity
        emit_mov_reg_imm(reg, value)
    end
    
    return reg
end

# Generate code for binary expression
fun generate_binary_expr(expr):
    set ast_data = get_ast_data(expr)
    set left_expr = ast_data[0]
    set operator = ast_data[1]
    set right_expr = ast_data[2]
    
    # Generate code for operands
    set left_reg = generate_expression(left_expr)
    set right_reg = generate_expression(right_expr)
    
    if left_reg == -1 || right_reg == -1:
        return -1
    end
    
    # Generate operation
    if operator == OP_ADD:
        emit_add_reg_reg(left_reg, right_reg)
    else if operator == OP_SUBTRACT:
        emit_sub_reg_reg(left_reg, right_reg)
    else if operator == OP_MULTIPLY:
        # Move right operand to RAX for multiplication
        if right_reg != REG_RAX:
            emit_mov_reg_imm(REG_RAX, 0)  # This would copy from right_reg
        end
        emit_mul_reg(left_reg)
        set left_reg = REG_RAX  # Result is in RAX
    end
    
    # Free the right register
    free_register(right_reg)
    
    return left_reg
end

# Generate code for identifier (variable access)
fun generate_identifier(expr):
    set ast_data = get_ast_data(expr)
    set name = ast_data[0]
    
    set offset = get_variable_offset(name)
    if offset == -1:
        add_codegen_error("Undefined variable: " + name, 0, 0, "")
        return -1
    end
    
    set reg = allocate_register()
    if reg == -1:
        add_codegen_error("No free registers available", 0, 0, "")
        return -1
    end
    
    # Load variable from stack (simplified)
    # This would generate: MOV reg, [RBP + offset]
    emit_byte(0x48)  # REX.W prefix
    emit_byte(0x8B)  # MOV r64, r/m64
    emit_byte(0x45 + reg * 8)  # ModR/M: [RBP + disp8]
    emit_byte(offset)  # 8-bit displacement
    
    return reg
end

# Generate code for expression
fun generate_expression(expr):
    if expr == null:
        return -1
    end
    
    set ast_type = get_ast_type(expr)
    
    if ast_type == AST_LITERAL_EXPR:
        return generate_literal(expr)
    end
    
    if ast_type == AST_BINARY_EXPR:
        return generate_binary_expr(expr)
    end
    
    if ast_type == AST_IDENTIFIER_EXPR:
        return generate_identifier(expr)
    end
    
    # Add more expression types as needed
    
    return -1
end

# Generate code for variable declaration
fun generate_var_decl(stmt):
    set ast_data = get_ast_data(stmt)
    set name = ast_data[0]
    set type_annotation = ast_data[1]
    set initializer = ast_data[2]
    set is_const = ast_data[3]
    
    # Allocate stack space for variable
    set current_stack_offset = current_stack_offset + 8  # 8 bytes for 64-bit value
    add_variable_offset(name, current_stack_offset)
    
    if initializer != null:
        # Generate code for initializer
        set init_reg = generate_expression(initializer)
        if init_reg == -1:
            return false
        end
        
        # Store value on stack
        # MOV [RBP - offset], reg
        emit_byte(0x48)  # REX.W prefix
        emit_byte(0x89)  # MOV r/m64, r64
        emit_byte(0x45 + init_reg * 8)  # ModR/M: [RBP - disp8]
        emit_byte(-current_stack_offset)  # Negative offset from RBP
        
        free_register(init_reg)
    end
    
    return true
end

# Generate code for say statement (simplified)
fun generate_say_stmt(stmt):
    set ast_data = get_ast_data(stmt)
    set expr = ast_data[0]
    
    # Generate code for expression
    set reg = generate_expression(expr)
    if reg == -1:
        return false
    end
    
    # For simplicity, we'll generate a system call to write
    # This is a very simplified version
    emit_mov_reg_imm(REG_RAX, 1)  # sys_write
    emit_mov_reg_imm(REG_RDI, 1)  # stdout
    # RSI would point to the string data
    # RDX would contain the length
    emit_syscall()
    
    free_register(reg)
    return true
end

# Generate code for statement
fun generate_statement(stmt):
    if stmt == null:
        return true
    end
    
    set ast_type = get_ast_type(stmt)
    
    if ast_type == AST_VAR_DECL:
        return generate_var_decl(stmt)
    end
    
    if ast_type == AST_SAY_STMT:
        return generate_say_stmt(stmt)
    end
    
    if ast_type == AST_EXPRESSION_STMT:
        set ast_data = get_ast_data(stmt)
        set expr = ast_data[0]
        set reg = generate_expression(expr)
        if reg != -1:
            free_register(reg)
        end
        return reg != -1
    end
    
    # Add more statement types as needed
    
    return true
end

# Generate function prologue
fun generate_function_prologue():
    emit_push_reg(REG_RBP)  # PUSH RBP
    # MOV RBP, RSP
    emit_byte(0x48)  # REX.W prefix
    emit_byte(0x89)  # MOV r/m64, r64
    emit_byte(0xE5)  # ModR/M: RBP, RSP
end

# Generate function epilogue
fun generate_function_epilogue():
    # MOV RSP, RBP
    emit_byte(0x48)  # REX.W prefix
    emit_byte(0x89)  # MOV r/m64, r64
    emit_byte(0xEC)  # ModR/M: RSP, RBP
    emit_pop_reg(REG_RBP)   # POP RBP
    emit_ret()              # RET
end

# Generate code for program
fun generate_program_code(ast):
    if ast == null:
        return null
    end
    
    set ast_type = get_ast_type(ast)
    if ast_type != AST_PROGRAM:
        add_codegen_error("Expected program AST node", 0, 0, "")
        return null
    end
    
    set statements = get_ast_data(ast)
    
    # Generate main function prologue
    generate_function_prologue()
    
    # Generate code for all statements
    set i = 0
    while i < array_length(statements):
        set success = generate_statement(statements[i])
        if !success:
            return null
        end
        set i = i + 1
    end
    
    # Generate main function epilogue
    generate_function_epilogue()
    
    return code_buffer
end

# Create executable header (simplified ELF header for Linux)
fun create_elf_header():
    set header = []
    
    # ELF magic number
    set header = header + [0x7F, 0x45, 0x4C, 0x46]  # "\x7FELF"
    
    # ELF class (64-bit)
    set header = header + [0x02]
    
    # Data encoding (little endian)
    set header = header + [0x01]
    
    # ELF version
    set header = header + [0x01]
    
    # OS/ABI (System V)
    set header = header + [0x00]
    
    # ABI version
    set header = header + [0x00]
    
    # Padding (7 bytes)
    set i = 0
    while i < 7:
        set header = header + [0x00]
        set i = i + 1
    end
    
    # Object file type (executable)
    set header = header + [0x02, 0x00]
    
    # Machine type (x86-64)
    set header = header + [0x3E, 0x00]
    
    # Version
    set header = header + [0x01, 0x00, 0x00, 0x00]
    
    # Entry point address (simplified)
    set i = 0
    while i < 8:
        set header = header + [0x00]
        set i = i + 1
    end
    
    # Program header offset
    set i = 0
    while i < 8:
        set header = header + [0x00]
        set i = i + 1
    end
    
    # Section header offset
    set i = 0
    while i < 8:
        set header = header + [0x00]
        set i = i + 1
    end
    
    # Flags
    set header = header + [0x00, 0x00, 0x00, 0x00]
    
    # ELF header size
    set header = header + [0x40, 0x00]
    
    # Program header entry size
    set header = header + [0x38, 0x00]
    
    # Number of program header entries
    set header = header + [0x01, 0x00]
    
    # Section header entry size
    set header = header + [0x40, 0x00]
    
    # Number of section header entries
    set header = header + [0x00, 0x00]
    
    # Section header string table index
    set header = header + [0x00, 0x00]
    
    return header
end

# Main code generation function
fun generate_code(ast):
    init_codegen()
    
    if ast == null:
        add_codegen_error("No AST provided for code generation", 0, 0, "")
        return null
    end
    
    # Generate machine code
    set machine_code = generate_program_code(ast)
    if machine_code == null:
        return null
    end
    
    # Create executable with ELF header
    set elf_header = create_elf_header()
    set executable = elf_header + machine_code
    
    return executable
end

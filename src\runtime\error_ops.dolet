# Dolet Runtime - Error Operations
# Runtime error handling and exception management for generated code

# Error types for runtime
const RUNTIME_ERROR_DIVISION_BY_ZERO = 1
const RUNTIME_ERROR_NULL_POINTER = 2
const RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS = 3
const RUNTIME_ERROR_STACK_OVERFLOW = 4
const RUNTIME_ERROR_MEMORY_ALLOCATION = 5
const RUNTIME_ERROR_TYPE_MISMATCH = 6
const RUNTIME_ERROR_INVALID_OPERATION = 7
const RUNTIME_ERROR_FILE_NOT_FOUND = 8
const RUNTIME_ERROR_PERMISSION_DENIED = 9
const RUNTIME_ERROR_ASSERTION_FAILED = 10

# Runtime error state
set runtime_errors = []
set error_handler_stack = []
set current_error = null
set error_count = 0

# Initialize runtime error system
fun init_runtime_errors():
    set runtime_errors = []
    set error_handler_stack = []
    set current_error = null
    set error_count = 0
end

# Create runtime error
fun create_runtime_error(error_type, message, function_name, line):
    return [error_type, message, function_name, line]
end

# Get runtime error properties
fun get_runtime_error_type(error):
    return error[0]
end

fun get_runtime_error_message(error):
    return error[1]
end

fun get_runtime_error_function(error):
    return error[2]
end

fun get_runtime_error_line(error):
    return error[3]
end

# Convert runtime error type to string
fun runtime_error_type_to_string(error_type):
    if error_type == RUNTIME_ERROR_DIVISION_BY_ZERO:
        return "Division by Zero"
    end
    if error_type == RUNTIME_ERROR_NULL_POINTER:
        return "Null Pointer Access"
    end
    if error_type == RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS:
        return "Array Index Out of Bounds"
    end
    if error_type == RUNTIME_ERROR_STACK_OVERFLOW:
        return "Stack Overflow"
    end
    if error_type == RUNTIME_ERROR_MEMORY_ALLOCATION:
        return "Memory Allocation Failed"
    end
    if error_type == RUNTIME_ERROR_TYPE_MISMATCH:
        return "Type Mismatch"
    end
    if error_type == RUNTIME_ERROR_INVALID_OPERATION:
        return "Invalid Operation"
    end
    if error_type == RUNTIME_ERROR_FILE_NOT_FOUND:
        return "File Not Found"
    end
    if error_type == RUNTIME_ERROR_PERMISSION_DENIED:
        return "Permission Denied"
    end
    if error_type == RUNTIME_ERROR_ASSERTION_FAILED:
        return "Assertion Failed"
    end
    return "Unknown Error"
end

# Throw runtime error
fun throw_runtime_error(error_type, message, function_name, line):
    set error = create_runtime_error(error_type, message, function_name, line)
    set current_error = error
    set runtime_errors = runtime_errors + [error]
    set error_count = error_count + 1
    
    # Print error immediately for debugging
    print_runtime_error(error)
    
    # In a real implementation, this would unwind the stack
    # For bootstrap, we'll just set the error state
    return false
end

# Check for runtime error
fun has_runtime_error():
    return current_error != null
end

# Get current runtime error
fun get_current_runtime_error():
    return current_error
end

# Clear current runtime error
fun clear_runtime_error():
    set current_error = null
end

# Print runtime error
fun print_runtime_error(error):
    set error_type_str = runtime_error_type_to_string(get_runtime_error_type(error))
    set message = get_runtime_error_message(error)
    set function_name = get_runtime_error_function(error)
    set line = get_runtime_error_line(error)
    
    say "Runtime Error: " + error_type_str
    if function_name != null && function_name != "":
        say "  in function: " + function_name
    end
    if line > 0:
        say "  at line: " + line
    end
    say "  message: " + message
end

# Print all runtime errors
fun print_all_runtime_errors():
    if array_length(runtime_errors) == 0:
        return
    end
    
    say "Runtime Errors:"
    say "==============="
    
    set i = 0
    while i < array_length(runtime_errors):
        print_runtime_error(runtime_errors[i])
        say ""
        set i = i + 1
    end
    
    say "Total runtime errors: " + error_count
end

# Safe division operation
fun safe_divide_int(a, b):
    if b == 0:
        throw_runtime_error(RUNTIME_ERROR_DIVISION_BY_ZERO, "Cannot divide " + a + " by zero", "safe_divide_int", 0)
        return 0
    end
    return a / b
end

fun safe_divide_float(a, b):
    if b == 0.0:
        throw_runtime_error(RUNTIME_ERROR_DIVISION_BY_ZERO, "Cannot divide " + a + " by zero", "safe_divide_float", 0)
        return 0.0
    end
    return a / b
end

# Safe modulo operation
fun safe_modulo(a, b):
    if b == 0:
        throw_runtime_error(RUNTIME_ERROR_DIVISION_BY_ZERO, "Cannot compute " + a + " modulo zero", "safe_modulo", 0)
        return 0
    end
    return a % b
end

# Safe array access
fun safe_array_get(arr, index):
    if arr == null:
        throw_runtime_error(RUNTIME_ERROR_NULL_POINTER, "Cannot access null array", "safe_array_get", 0)
        return null
    end
    
    set len = array_length(arr)
    if index < 0 || index >= len:
        throw_runtime_error(RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS, "Index " + index + " out of bounds for array of length " + len, "safe_array_get", 0)
        return null
    end
    
    return arr[index]
end

# Safe array set
fun safe_array_set(arr, index, value):
    if arr == null:
        throw_runtime_error(RUNTIME_ERROR_NULL_POINTER, "Cannot modify null array", "safe_array_set", 0)
        return false
    end
    
    set len = array_length(arr)
    if index < 0 || index >= len:
        throw_runtime_error(RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS, "Index " + index + " out of bounds for array of length " + len, "safe_array_set", 0)
        return false
    end
    
    set arr[index] = value
    return true
end

# Safe string access
fun safe_string_char_at(str, index):
    if str == null:
        throw_runtime_error(RUNTIME_ERROR_NULL_POINTER, "Cannot access null string", "safe_string_char_at", 0)
        return '\0'
    end
    
    set len = length(str)
    if index < 0 || index >= len:
        throw_runtime_error(RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS, "Index " + index + " out of bounds for string of length " + len, "safe_string_char_at", 0)
        return '\0'
    end
    
    return str[index]
end

# Assert function
fun assert(condition, message):
    if !condition:
        if message == null:
            set message = "Assertion failed"
        end
        throw_runtime_error(RUNTIME_ERROR_ASSERTION_FAILED, message, "assert", 0)
        return false
    end
    return true
end

# Assert with custom error type
fun assert_with_type(condition, error_type, message):
    if !condition:
        if message == null:
            set message = "Assertion failed"
        end
        throw_runtime_error(error_type, message, "assert_with_type", 0)
        return false
    end
    return true
end

# Check null pointer
fun check_not_null(ptr, name):
    if ptr == null:
        throw_runtime_error(RUNTIME_ERROR_NULL_POINTER, "Null pointer: " + name, "check_not_null", 0)
        return false
    end
    return true
end

# Check array bounds
fun check_array_bounds(arr, index, operation):
    if arr == null:
        throw_runtime_error(RUNTIME_ERROR_NULL_POINTER, "Null array in " + operation, "check_array_bounds", 0)
        return false
    end
    
    set len = array_length(arr)
    if index < 0 || index >= len:
        throw_runtime_error(RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS, "Index " + index + " out of bounds in " + operation, "check_array_bounds", 0)
        return false
    end
    
    return true
end

# Check string bounds
fun check_string_bounds(str, index, operation):
    if str == null:
        throw_runtime_error(RUNTIME_ERROR_NULL_POINTER, "Null string in " + operation, "check_string_bounds", 0)
        return false
    end
    
    set len = length(str)
    if index < 0 || index >= len:
        throw_runtime_error(RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS, "Index " + index + " out of bounds in " + operation, "check_string_bounds", 0)
        return false
    end
    
    return true
end

# Stack overflow check (simplified)
set stack_depth = 0
const MAX_STACK_DEPTH = 1000

fun enter_function(function_name):
    set stack_depth = stack_depth + 1
    if stack_depth > MAX_STACK_DEPTH:
        throw_runtime_error(RUNTIME_ERROR_STACK_OVERFLOW, "Maximum stack depth exceeded in " + function_name, function_name, 0)
        return false
    end
    return true
end

fun exit_function():
    if stack_depth > 0:
        set stack_depth = stack_depth - 1
    end
end

# Get stack depth
fun get_stack_depth():
    return stack_depth
end

# Memory allocation check
fun check_memory_allocation(ptr, size, operation):
    if ptr == null && size > 0:
        throw_runtime_error(RUNTIME_ERROR_MEMORY_ALLOCATION, "Memory allocation failed for " + size + " bytes in " + operation, operation, 0)
        return false
    end
    return true
end

# Type checking helpers
fun check_type_int(value, operation):
    # Simple type check - in real implementation would be more sophisticated
    if value != value + 0 - 0:  # Check if it behaves like a number
        throw_runtime_error(RUNTIME_ERROR_TYPE_MISMATCH, "Expected integer in " + operation, operation, 0)
        return false
    end
    return true
end

fun check_type_string(value, operation):
    # Simple type check
    if value + "" != value:
        throw_runtime_error(RUNTIME_ERROR_TYPE_MISMATCH, "Expected string in " + operation, operation, 0)
        return false
    end
    return true
end

fun check_type_array(value, operation):
    if value == null:
        throw_runtime_error(RUNTIME_ERROR_TYPE_MISMATCH, "Expected array, got null in " + operation, operation, 0)
        return false
    end
    # In real implementation, would check if it's actually an array
    return true
end

# Error recovery helpers
fun try_recover_from_error():
    if current_error == null:
        return true
    end
    
    set error_type = get_runtime_error_type(current_error)
    
    # Some errors can be recovered from
    if error_type == RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS ||
       error_type == RUNTIME_ERROR_NULL_POINTER:
        clear_runtime_error()
        return true
    end
    
    # Other errors are fatal
    return false
end

# Get error statistics
fun get_error_statistics():
    set division_by_zero = 0
    set null_pointer = 0
    set array_bounds = 0
    set other_errors = 0
    
    set i = 0
    while i < array_length(runtime_errors):
        set error = runtime_errors[i]
        set error_type = get_runtime_error_type(error)
        
        if error_type == RUNTIME_ERROR_DIVISION_BY_ZERO:
            set division_by_zero = division_by_zero + 1
        else if error_type == RUNTIME_ERROR_NULL_POINTER:
            set null_pointer = null_pointer + 1
        else if error_type == RUNTIME_ERROR_ARRAY_OUT_OF_BOUNDS:
            set array_bounds = array_bounds + 1
        else:
            set other_errors = other_errors + 1
        end
        set i = i + 1
    end
    
    # Return: [total, division_by_zero, null_pointer, array_bounds, other_errors]
    return [error_count, division_by_zero, null_pointer, array_bounds, other_errors]
end

# Print error statistics
fun print_error_statistics():
    set stats = get_error_statistics()
    set total = stats[0]
    set div_zero = stats[1]
    set null_ptr = stats[2]
    set array_bounds = stats[3]
    set other = stats[4]
    
    say "Runtime Error Statistics:"
    say "========================"
    say "Total errors: " + total
    say "Division by zero: " + div_zero
    say "Null pointer access: " + null_ptr
    say "Array bounds violations: " + array_bounds
    say "Other errors: " + other
end

# Cleanup runtime error system
fun cleanup_runtime_errors():
    set runtime_errors = []
    set error_handler_stack = []
    set current_error = null
    set error_count = 0
    set stack_depth = 0
end

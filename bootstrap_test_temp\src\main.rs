use std::io::{self, Write};

fn create_token(token_type: i64, lexeme: i64, line: i64, column: i64) -> i64 {
    let mut token = vec![token_type, lexeme, line, column];
    return token;
    0
}

fn get_token_type(token: i64) -> i64 {
    return token[0 as usize];
    0
}

fn get_token_lexeme(token: i64) -> i64 {
    return token[1 as usize];
    0
}

fn create_ast_node(node_type: i64, data: i64) -> i64 {
    return vec![node_type, data];
    0
}

fn get_ast_type(node: i64) -> i64 {
    return node[0 as usize];
    0
}

fn get_ast_data(node: i64) -> i64 {
    return node[1 as usize];
    0
}

fn add_symbol(name: i64, symbol_type: i64) -> bool {
    let mut symbol = (name, symbol_type, false, false,);
    let mut symbols = (symbols + vec![symbol]);
    return true;
    false
}

fn find_symbol(name: i64) -> i64 {
    let mut i = 0;
    while (i < array_length(symbols)) {
        let mut symbol = symbols[i as usize];
        if (symbol[0 as usize] == name) {
            // Unsupported nested statement in function while loop
        }
        let mut i = format!("{}{}" , i, 1);
    }
    return "null";
    0
}

fn infer_type_from_value(value: i64) -> i64 {
    if (value == true) {
        return TYPE_BOOL;
    }
    if (value == false) {
        return TYPE_BOOL;
    }
    if (value == 0) {
        return TYPE_INT;
    }
    if (value != 0) {
        let mut test = ((value + 1) - 1);
        if (test == value) {
            return TYPE_INT;
        }
    }
    return TYPE_STRING;
    0
}

fn add_error(message: i64, line: i64) {
    let mut error = vec![message, line];
    let mut errors = (errors + vec![error]);
}

fn has_errors() -> bool {
    return (array_length(errors) > 0);
    false
}

fn print_errors() {
    if (!has_errors()) {
        println!("{}", "No errors found");
        // Unsupported statement in if
    }
    println!("{}", "Errors found:");
    let mut i = 0;
    while (i < array_length(errors)) {
        let mut error = errors[i as usize];
        println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , "  Line ", error[1 as usize]), ": "), error[0 as usize]));
        let mut i = format!("{}{}" , i, 1);
    }
}

fn simulate_compilation(source_description: &str) -> bool {
    println!("{}", format!("{}{}" , "Compiling: ", source_description));
    println!("{}", "  Phase 1: Tokenization - OK");
    println!("{}", "  Phase 2: Parsing - OK");
    println!("{}", "  Phase 3: Semantic Analysis - OK");
    println!("{}", "  Phase 4: Type Inference - OK");
    println!("{}", "  Phase 5: Code Generation - OK");
    println!("{}", "  Compilation successful!");
    return true;
    false
}

fn allocate_memory(size: i64) -> i64 {
    let mut block = (size, true,);
    let mut allocated_blocks = (allocated_blocks + vec![block]);
    return (array_length(allocated_blocks) - 1);
    0
}

fn free_memory(pointer: i64) -> bool {
    if (pointer >= 0) {
        if (pointer < array_length(allocated_blocks)) {
            // Unsupported nested statement
            // Unsupported nested statement
            return true;
        }
    }
    return false;
    false
}

fn get_memory_stats() -> i64 {
    let mut total_allocated = 0;
    let mut active_blocks = 0;
    let mut i = 0;
    while (i < array_length(allocated_blocks)) {
        let mut block = allocated_blocks[i as usize];
        if block[1 as usize] {
            total_allocated = (total_allocated + block[0 as usize]);
            active_blocks = (active_blocks + 1);
        }
        let mut i = format!("{}{}" , i, 1);
    }
    return vec![total_allocated, active_blocks];
    0
}

const TOKEN_INTEGER: i64 = 1;
const TOKEN_STRING: i64 = 4;
const TOKEN_IDENTIFIER: i64 = 8;
const TOKEN_PLUS: i64 = 40;
const TOKEN_ASSIGN: i64 = 45;
const AST_PROGRAM: i64 = 1;
const AST_VAR_DECL: i64 = 2;
const AST_LITERAL_EXPR: i64 = 22;
const TYPE_INT: i64 = 1;
const TYPE_STRING: i64 = 4;
const TYPE_BOOL: i64 = 6;

fn main() {
    println!("{}", "Testing Dolet Self-Hosting Compiler Components");
    println!("{}", "==============================================");
    println!("{}", "Testing basic tokenization...");
    let mut int_token = create_token(TOKEN_INTEGER, "42".to_string(), 1, 1);
    let mut str_token = create_token(TOKEN_STRING, "hello".to_string(), 1, 5);
    let mut id_token = create_token(TOKEN_IDENTIFIER, "variable".to_string(), 1, 10);
    println!("{}", format!("{}{}" , "Created integer token: ", get_token_lexeme(int_token)));
    println!("{}", format!("{}{}" , "Created string token: ", get_token_lexeme(str_token)));
    println!("{}", format!("{}{}" , "Created identifier token: ", get_token_lexeme(id_token)));
    println!("{}", "");
    println!("{}", "Testing AST node creation...");
    let mut literal_node = create_ast_node(AST_LITERAL_EXPR, vec![42]);
    let mut var_decl_node = create_ast_node(AST_VAR_DECL, ("x", "null", literal_node, false,));
    println!("{}", format!("{}{}" , "Created literal AST node with value: ", get_ast_data(literal_node)[0 as usize]));
    println!("{}", format!("{}{}" , "Created variable declaration AST node for: ", get_ast_data(var_decl_node)[0 as usize]));
    println!("{}", "");
    println!("{}", "Testing symbol table...");
    let mut symbols = vec![0i64; 0];
    add_symbol("x", 1);
    add_symbol("message", 4);
    let mut found_x = find_symbol("x".to_string());
    if (found_x != "null") {
        println!("{}", format!("{}{}" , "Found symbol 'x' with type: ", found_x[1 as usize]));
    } else {
        println!("{}", "Symbol 'x' not found");
    }
    let mut found_message = find_symbol("message".to_string());
    if (found_message != "null") {
        println!("{}", format!("{}{}" , "Found symbol 'message' with type: ", found_message[1 as usize]));
    } else {
        println!("{}", "Symbol 'message' not found");
    }
    println!("{}", "");
    println!("{}", "Testing type inference...");
    let mut type1 = infer_type_from_value(42);
    let mut type2 = infer_type_from_value("hello".to_string());
    let mut type3 = infer_type_from_value(true);
    println!("{}", format!("{}{}" , "Type of 42: ", type1));
    println!("{}", format!("{}{}" , "Type of 'hello': ", type2));
    println!("{}", format!("{}{}" , "Type of true: ", type3));
    println!("{}", "");
    println!("{}", "Testing error handling...");
    let mut errors = vec![0i64; 0];
    add_error("Undefined variable 'y'", 10);
    add_error("Type mismatch", 15);
    print_errors();
    println!("{}", "");
    println!("{}", "Testing compilation pipeline...");
    simulate_compilation("test program");
    println!("{}", "");
    println!("{}", "Testing memory management...");
    let mut allocated_blocks = vec![0i64; 0];
    let mut ptr1 = allocate_memory(100);
    let mut ptr2 = allocate_memory(200);
    let mut ptr3 = allocate_memory(50);
    let mut stats = get_memory_stats();
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "Allocated ", stats[1 as usize]), " blocks, "), stats[0 as usize]), " bytes total"));
    free_memory(ptr2);
    let mut stats = get_memory_stats();
    println!("{}", format!("{}{}" , format!("{}{}" , format!("{}{}" , format!("{}{}" , "After freeing one block: ", stats[1 as usize]), " blocks, "), stats[0 as usize]), " bytes total"));
    println!("{}", "");
    println!("{}", "All tests completed successfully!");
    println!("{}", "The self-hosting compiler components are working correctly.");
    println!("{}", "");
    println!("{}", "Next step: Use the bootstrap compiler to compile the full self-hosting compiler.");
}

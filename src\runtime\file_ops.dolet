# Dolet Runtime - File Operations
# High-performance file I/O functions for the Dolet runtime

# File operation constants
const FILE_READ = 1
const FILE_WRITE = 2
const FILE_APPEND = 3
const FILE_READ_WRITE = 4

# File handle structure (simplified)
# [filename, mode, is_open, position, size]
set open_files = []

# Create file handle
fun create_file_handle(filename, mode):
    return [filename, mode, false, 0, 0]
end

# Get file handle properties
fun get_file_name(handle):
    return handle[0]
end

fun get_file_mode(handle):
    return handle[1]
end

fun is_file_open(handle):
    return handle[2]
end

fun get_file_position(handle):
    return handle[3]
end

fun get_file_size(handle):
    return handle[4]
end

fun set_file_open(handle, is_open):
    set handle[2] = is_open
end

fun set_file_position(handle, position):
    set handle[3] = position
end

fun set_file_size(handle, size):
    set handle[4] = size
end

# Open file
fun file_open(filename, mode):
    if filename == null || filename == "":
        return null
    end
    
    # Check if file is already open
    set i = 0
    while i < array_length(open_files):
        set handle = open_files[i]
        if get_file_name(handle) == filename && is_file_open(handle):
            return handle  # Return existing handle
        end
        set i = i + 1
    end
    
    # Create new file handle
    set handle = create_file_handle(filename, mode)
    
    # In a real implementation, this would make system calls
    # For bootstrap, we'll simulate file operations
    set_file_open(handle, true)
    set_file_position(handle, 0)
    
    # Simulate file size (in real implementation, would get from filesystem)
    if mode == FILE_READ || mode == FILE_READ_WRITE:
        set_file_size(handle, 1024)  # Placeholder size
    else:
        set_file_size(handle, 0)
    end
    
    set open_files = open_files + [handle]
    return handle
end

# Close file
fun file_close(handle):
    if handle == null || !is_file_open(handle):
        return false
    end
    
    set_file_open(handle, false)
    
    # Remove from open files list
    set new_open_files = []
    set i = 0
    while i < array_length(open_files):
        set file_handle = open_files[i]
        if file_handle != handle:
            set new_open_files = new_open_files + [file_handle]
        end
        set i = i + 1
    end
    set open_files = new_open_files
    
    return true
end

# Read from file
fun file_read(handle, buffer_size):
    if handle == null || !is_file_open(handle):
        return null
    end
    
    set mode = get_file_mode(handle)
    if mode != FILE_READ && mode != FILE_READ_WRITE:
        return null  # File not open for reading
    end
    
    set position = get_file_position(handle)
    set file_size = get_file_size(handle)
    
    if position >= file_size:
        return ""  # End of file
    end
    
    # Calculate how much to read
    set bytes_to_read = buffer_size
    if position + bytes_to_read > file_size:
        set bytes_to_read = file_size - position
    end
    
    # In a real implementation, this would read from filesystem
    # For bootstrap, we'll return placeholder data
    set data = "Sample file content for position " + position
    
    # Update file position
    set_file_position(handle, position + bytes_to_read)
    
    return data
end

# Write to file
fun file_write(handle, data):
    if handle == null || !is_file_open(handle):
        return false
    end
    
    set mode = get_file_mode(handle)
    if mode != FILE_WRITE && mode != FILE_APPEND && mode != FILE_READ_WRITE:
        return false  # File not open for writing
    end
    
    if data == null:
        return true  # Nothing to write
    end
    
    set position = get_file_position(handle)
    set data_length = length(data)
    
    # In a real implementation, this would write to filesystem
    # For bootstrap, we'll simulate the operation
    
    # Update file position and size
    if mode == FILE_APPEND:
        set file_size = get_file_size(handle)
        set_file_position(handle, file_size)
        set_file_size(handle, file_size + data_length)
    else:
        set new_position = position + data_length
        set_file_position(handle, new_position)
        
        set current_size = get_file_size(handle)
        if new_position > current_size:
            set_file_size(handle, new_position)
        end
    end
    
    return true
end

# Read entire file as string
fun file_read_all(filename):
    set handle = file_open(filename, FILE_READ)
    if handle == null:
        return null
    end
    
    set content = ""
    set file_size = get_file_size(handle)
    
    if file_size > 0:
        set content = file_read(handle, file_size)
    end
    
    file_close(handle)
    return content
end

# Write string to file (overwrite)
fun file_write_all(filename, content):
    set handle = file_open(filename, FILE_WRITE)
    if handle == null:
        return false
    end
    
    set success = file_write(handle, content)
    file_close(handle)
    
    return success
end

# Append string to file
fun file_append_all(filename, content):
    set handle = file_open(filename, FILE_APPEND)
    if handle == null:
        return false
    end
    
    set success = file_write(handle, content)
    file_close(handle)
    
    return success
end

# Check if file exists
fun file_exists(filename):
    if filename == null || filename == "":
        return false
    end
    
    # In a real implementation, this would check filesystem
    # For bootstrap, we'll simulate based on filename
    if filename == "test.dolet" || filename == "main.dolet":
        return true
    end
    
    return false
end

# Get file size
fun file_get_size(filename):
    if !file_exists(filename):
        return -1
    end
    
    set handle = file_open(filename, FILE_READ)
    if handle == null:
        return -1
    end
    
    set size = get_file_size(handle)
    file_close(handle)
    
    return size
end

# Delete file
fun file_delete(filename):
    if filename == null || filename == "":
        return false
    end
    
    # Close file if it's open
    set i = 0
    while i < array_length(open_files):
        set handle = open_files[i]
        if get_file_name(handle) == filename && is_file_open(handle):
            file_close(handle)
        end
        set i = i + 1
    end
    
    # In a real implementation, this would delete from filesystem
    # For bootstrap, we'll simulate success
    return true
end

# Copy file
fun file_copy(source_filename, dest_filename):
    if source_filename == null || dest_filename == null:
        return false
    end
    
    set content = file_read_all(source_filename)
    if content == null:
        return false
    end
    
    return file_write_all(dest_filename, content)
end

# Move/rename file
fun file_move(old_filename, new_filename):
    if old_filename == null || new_filename == null:
        return false
    end
    
    # Copy then delete
    if file_copy(old_filename, new_filename):
        return file_delete(old_filename)
    end
    
    return false
end

# Read file line by line
fun file_read_lines(filename):
    set content = file_read_all(filename)
    if content == null:
        return null
    end
    
    set lines = []
    set current_line = ""
    set i = 0
    
    while i < length(content):
        set ch = content[i]
        if ch == '\n':
            set lines = lines + [current_line]
            set current_line = ""
        else if ch != '\r':  # Skip carriage return
            set current_line = current_line + ch
        end
        set i = i + 1
    end
    
    # Add last line if not empty
    if length(current_line) > 0:
        set lines = lines + [current_line]
    end
    
    return lines
end

# Write lines to file
fun file_write_lines(filename, lines):
    if lines == null:
        return false
    end
    
    set content = ""
    set i = 0
    while i < array_length(lines):
        set content = content + lines[i]
        if i < array_length(lines) - 1:
            set content = content + "\n"
        end
        set i = i + 1
    end
    
    return file_write_all(filename, content)
end

# Get file extension
fun file_get_extension(filename):
    if filename == null || filename == "":
        return ""
    end
    
    set last_dot = -1
    set i = 0
    while i < length(filename):
        if filename[i] == '.':
            set last_dot = i
        end
        set i = i + 1
    end
    
    if last_dot == -1 || last_dot == length(filename) - 1:
        return ""
    end
    
    return substring(filename, last_dot + 1, length(filename) - last_dot - 1)
end

# Get filename without extension
fun file_get_name_without_extension(filename):
    if filename == null || filename == "":
        return ""
    end
    
    set last_dot = -1
    set i = 0
    while i < length(filename):
        if filename[i] == '.':
            set last_dot = i
        end
        set i = i + 1
    end
    
    if last_dot == -1:
        return filename
    end
    
    return substring(filename, 0, last_dot)
end

# Get directory from path
fun file_get_directory(filepath):
    if filepath == null || filepath == "":
        return ""
    end
    
    set last_slash = -1
    set i = 0
    while i < length(filepath):
        if filepath[i] == '/' || filepath[i] == '\\':
            set last_slash = i
        end
        set i = i + 1
    end
    
    if last_slash == -1:
        return ""
    end
    
    return substring(filepath, 0, last_slash)
end

# Get filename from path
fun file_get_filename(filepath):
    if filepath == null || filepath == "":
        return ""
    end
    
    set last_slash = -1
    set i = 0
    while i < length(filepath):
        if filepath[i] == '/' || filepath[i] == '\\':
            set last_slash = i
        end
        set i = i + 1
    end
    
    if last_slash == -1:
        return filepath
    end
    
    return substring(filepath, last_slash + 1, length(filepath) - last_slash - 1)
end

# Close all open files
fun file_close_all():
    set i = 0
    while i < array_length(open_files):
        set handle = open_files[i]
        if is_file_open(handle):
            file_close(handle)
        end
        set i = i + 1
    end
    set open_files = []
end

# Get list of open files
fun file_get_open_files():
    set open_file_names = []
    set i = 0
    while i < array_length(open_files):
        set handle = open_files[i]
        if is_file_open(handle):
            set open_file_names = open_file_names + [get_file_name(handle)]
        end
        set i = i + 1
    end
    return open_file_names
end

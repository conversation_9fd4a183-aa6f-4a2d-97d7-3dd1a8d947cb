# Dolet Runtime - Array Operations
# High-performance array manipulation functions for the Dolet runtime

# Create a new array with specified size
fun create_array(size):
    if size <= 0:
        return []
    end
    
    set arr = []
    set i = 0
    while i < size:
        set arr = arr + [null]
        set i = i + 1
    end
    return arr
end

# Get array length
fun array_length(arr):
    if arr == null:
        return 0
    end
    
    # This would be implemented at machine code level
    # For now, we'll count elements
    set count = 0
    set i = 0
    while i < 1000000:  # Safety limit
        # In real implementation, this would check array bounds
        if i >= array_size(arr):
            return count
        end
        set count = count + 1
        set i = i + 1
    end
    return count
end

# Get element at index (safe access)
fun array_get(arr, index):
    if arr == null:
        return null
    end
    if index < 0:
        return null
    end
    if index >= array_length(arr):
        return null
    end
    
    return arr[index]
end

# Set element at index (safe access)
fun array_set(arr, index, value):
    if arr == null:
        return false
    end
    if index < 0:
        return false
    end
    if index >= array_length(arr):
        return false
    end
    
    set arr[index] = value
    return true
end

# Add element to end of array
fun array_push(arr, element):
    if arr == null:
        return [element]
    end
    
    return arr + [element]
end

# Remove and return last element
fun array_pop(arr):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    if len == 0:
        return null
    end
    
    set last_element = arr[len - 1]
    
    # Create new array without last element
    set new_arr = []
    set i = 0
    while i < len - 1:
        set new_arr = new_arr + [arr[i]]
        set i = i + 1
    end
    
    return last_element
end

# Insert element at specific index
fun array_insert(arr, index, element):
    if arr == null:
        if index == 0:
            return [element]
        else:
            return null
        end
    end
    
    set len = array_length(arr)
    if index < 0 || index > len:
        return arr  # Invalid index, return original array
    end
    
    set new_arr = []
    set i = 0
    
    # Copy elements before insertion point
    while i < index:
        set new_arr = new_arr + [arr[i]]
        set i = i + 1
    end
    
    # Insert new element
    set new_arr = new_arr + [element]
    
    # Copy remaining elements
    while i < len:
        set new_arr = new_arr + [arr[i]]
        set i = i + 1
    end
    
    return new_arr
end

# Remove element at specific index
fun array_remove(arr, index):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    if index < 0 || index >= len:
        return arr  # Invalid index, return original array
    end
    
    set new_arr = []
    set i = 0
    
    # Copy elements before removal point
    while i < index:
        set new_arr = new_arr + [arr[i]]
        set i = i + 1
    end
    
    # Skip the element to remove
    set i = i + 1
    
    # Copy remaining elements
    while i < len:
        set new_arr = new_arr + [arr[i]]
        set i = i + 1
    end
    
    return new_arr
end

# Find index of element in array
fun array_index_of(arr, element):
    if arr == null:
        return -1
    end
    
    set len = array_length(arr)
    set i = 0
    while i < len:
        if arr[i] == element:
            return i
        end
        set i = i + 1
    end
    
    return -1
end

# Check if array contains element
fun array_contains(arr, element):
    return array_index_of(arr, element) != -1
end

# Reverse array
fun array_reverse(arr):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    if len <= 1:
        return arr
    end
    
    set new_arr = []
    set i = len - 1
    while i >= 0:
        set new_arr = new_arr + [arr[i]]
        set i = i - 1
    end
    
    return new_arr
end

# Get subarray (slice)
fun array_slice(arr, start, length):
    if arr == null:
        return null
    end
    
    set arr_len = array_length(arr)
    if start < 0:
        set start = 0
    end
    if start >= arr_len:
        return []
    end
    if length < 0:
        return []
    end
    
    set end_index = start + length
    if end_index > arr_len:
        set end_index = arr_len
    end
    
    set new_arr = []
    set i = start
    while i < end_index:
        set new_arr = new_arr + [arr[i]]
        set i = i + 1
    end
    
    return new_arr
end

# Concatenate two arrays
fun array_concat(arr1, arr2):
    if arr1 == null && arr2 == null:
        return []
    end
    if arr1 == null:
        return arr2
    end
    if arr2 == null:
        return arr1
    end
    
    return arr1 + arr2
end

# Fill array with value
fun array_fill(arr, value):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    set new_arr = []
    set i = 0
    while i < len:
        set new_arr = new_arr + [value]
        set i = i + 1
    end
    
    return new_arr
end

# Copy array
fun array_copy(arr):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    set new_arr = []
    set i = 0
    while i < len:
        set new_arr = new_arr + [arr[i]]
        set i = i + 1
    end
    
    return new_arr
end

# Check if two arrays are equal
fun array_equals(arr1, arr2):
    if arr1 == null && arr2 == null:
        return true
    end
    if arr1 == null || arr2 == null:
        return false
    end
    
    set len1 = array_length(arr1)
    set len2 = array_length(arr2)
    
    if len1 != len2:
        return false
    end
    
    set i = 0
    while i < len1:
        if arr1[i] != arr2[i]:
            return false
        end
        set i = i + 1
    end
    
    return true
end

# Simple bubble sort for integers
fun array_sort_int(arr):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    if len <= 1:
        return arr
    end
    
    set sorted_arr = array_copy(arr)
    
    set i = 0
    while i < len - 1:
        set j = 0
        while j < len - 1 - i:
            if sorted_arr[j] > sorted_arr[j + 1]:
                # Swap elements
                set temp = sorted_arr[j]
                set sorted_arr[j] = sorted_arr[j + 1]
                set sorted_arr[j + 1] = temp
            end
            set j = j + 1
        end
        set i = i + 1
    end
    
    return sorted_arr
end

# Find minimum value in integer array
fun array_min_int(arr):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    if len == 0:
        return null
    end
    
    set min_val = arr[0]
    set i = 1
    while i < len:
        if arr[i] < min_val:
            set min_val = arr[i]
        end
        set i = i + 1
    end
    
    return min_val
end

# Find maximum value in integer array
fun array_max_int(arr):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    if len == 0:
        return null
    end
    
    set max_val = arr[0]
    set i = 1
    while i < len:
        if arr[i] > max_val:
            set max_val = arr[i]
        end
        set i = i + 1
    end
    
    return max_val
end

# Sum all integers in array
fun array_sum_int(arr):
    if arr == null:
        return 0
    end
    
    set len = array_length(arr)
    set sum = 0
    set i = 0
    while i < len:
        set sum = sum + arr[i]
        set i = i + 1
    end
    
    return sum
end

# Calculate average of integers in array
fun array_average_int(arr):
    if arr == null:
        return 0.0
    end
    
    set len = array_length(arr)
    if len == 0:
        return 0.0
    end
    
    set sum = array_sum_int(arr)
    return sum / len
end

# Filter array based on predicate (placeholder for function pointers)
fun array_filter_positive_int(arr):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    set filtered = []
    set i = 0
    while i < len:
        if arr[i] > 0:
            set filtered = filtered + [arr[i]]
        end
        set i = i + 1
    end
    
    return filtered
end

# Map array to new array (placeholder - doubles each element)
fun array_map_double_int(arr):
    if arr == null:
        return null
    end
    
    set len = array_length(arr)
    set mapped = []
    set i = 0
    while i < len:
        set mapped = mapped + [arr[i] * 2]
        set i = i + 1
    end
    
    return mapped
end

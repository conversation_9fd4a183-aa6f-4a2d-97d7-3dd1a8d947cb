# Dolet Runtime - String Operations
# High-performance string manipulation functions for the Dolet runtime

# String length function
fun length(str):
    if str == null:
        return 0
    end
    
    set count = 0
    set i = 0
    while i < 1000000:  # Safety limit
        # This would be implemented at the machine code level
        # For now, we'll use a placeholder approach
        if str[i] == '\0':
            return count
        end
        set count = count + 1
        set i = i + 1
    end
    return count
end

# String concatenation
fun concat(str1, str2):
    if str1 == null:
        set str1 = ""
    end
    if str2 == null:
        set str2 = ""
    end
    
    # This would be implemented at machine code level
    # For bootstrap, we use the + operator
    return str1 + str2
end

# String substring
fun substring(str, start, length):
    if str == null:
        return ""
    end
    if start < 0:
        set start = 0
    end
    if length < 0:
        return ""
    end
    
    set result = ""
    set i = start
    set end_pos = start + length
    
    while i < end_pos:
        if str[i] == '\0':
            return result
        end
        set result = result + str[i]
        set i = i + 1
    end
    
    return result
end

# String character at index
fun char_at(str, index):
    if str == null:
        return '\0'
    end
    if index < 0:
        return '\0'
    end
    
    # This would be implemented at machine code level
    return str[index]
end

# String comparison
fun string_equals(str1, str2):
    if str1 == null && str2 == null:
        return true
    end
    if str1 == null || str2 == null:
        return false
    end
    
    set len1 = length(str1)
    set len2 = length(str2)
    
    if len1 != len2:
        return false
    end
    
    set i = 0
    while i < len1:
        if str1[i] != str2[i]:
            return false
        end
        set i = i + 1
    end
    
    return true
end

# String to integer conversion
fun string_to_int(str):
    if str == null:
        return 0
    end
    
    set result = 0
    set i = 0
    set negative = false
    
    # Skip whitespace
    while i < length(str) && str[i] == ' ':
        set i = i + 1
    end
    
    # Check for negative sign
    if i < length(str) && str[i] == '-':
        set negative = true
        set i = i + 1
    end
    
    # Convert digits
    while i < length(str):
        set ch = str[i]
        if ch >= '0' && ch <= '9':
            set digit = ch - '0'
            set result = result * 10 + digit
        else:
            return 0  # Invalid character
        end
        set i = i + 1
    end
    
    if negative:
        return -result
    end
    return result
end

# Integer to string conversion
fun int_to_string(num):
    if num == 0:
        return "0"
    end
    
    set negative = false
    if num < 0:
        set negative = true
        set num = -num
    end
    
    set result = ""
    while num > 0:
        set digit = num % 10
        set digit_char = '0' + digit
        set result = digit_char + result
        set num = num / 10
    end
    
    if negative:
        set result = "-" + result
    end
    
    return result
end

# String to float conversion
fun string_to_float(str):
    if str == null:
        return 0.0
    end
    
    set result = 0.0
    set i = 0
    set negative = false
    set decimal_places = 0
    set found_decimal = false
    
    # Skip whitespace
    while i < length(str) && str[i] == ' ':
        set i = i + 1
    end
    
    # Check for negative sign
    if i < length(str) && str[i] == '-':
        set negative = true
        set i = i + 1
    end
    
    # Convert digits
    while i < length(str):
        set ch = str[i]
        if ch >= '0' && ch <= '9':
            set digit = ch - '0'
            if found_decimal:
                set decimal_places = decimal_places + 1
                set result = result + digit / (10.0 * decimal_places)
            else:
                set result = result * 10.0 + digit
            end
        else if ch == '.' && !found_decimal:
            set found_decimal = true
        else:
            return 0.0  # Invalid character
        end
        set i = i + 1
    end
    
    if negative:
        return -result
    end
    return result
end

# Float to string conversion
fun float_to_string(num):
    if num == 0.0:
        return "0.0"
    end
    
    set negative = false
    if num < 0.0:
        set negative = true
        set num = -num
    end
    
    # Get integer part
    set int_part = num
    set result = int_to_string(int_part)
    
    # Get fractional part
    set frac_part = num - int_part
    if frac_part > 0.0:
        set result = result + "."
        set precision = 6  # 6 decimal places
        set i = 0
        while i < precision && frac_part > 0.0:
            set frac_part = frac_part * 10.0
            set digit = frac_part
            set result = result + int_to_string(digit)
            set frac_part = frac_part - digit
            set i = i + 1
        end
    else:
        set result = result + ".0"
    end
    
    if negative:
        set result = "-" + result
    end
    
    return result
end

# String contains substring
fun string_contains(str, substr):
    if str == null || substr == null:
        return false
    end
    
    set str_len = length(str)
    set substr_len = length(substr)
    
    if substr_len == 0:
        return true
    end
    if substr_len > str_len:
        return false
    end
    
    set i = 0
    while i <= str_len - substr_len:
        set match = true
        set j = 0
        while j < substr_len:
            if str[i + j] != substr[j]:
                set match = false
                return false
            end
            set j = j + 1
        end
        if match:
            return true
        end
        set i = i + 1
    end
    
    return false
end

# String index of substring
fun string_index_of(str, substr):
    if str == null || substr == null:
        return -1
    end
    
    set str_len = length(str)
    set substr_len = length(substr)
    
    if substr_len == 0:
        return 0
    end
    if substr_len > str_len:
        return -1
    end
    
    set i = 0
    while i <= str_len - substr_len:
        set match = true
        set j = 0
        while j < substr_len:
            if str[i + j] != substr[j]:
                set match = false
                return -1
            end
            set j = j + 1
        end
        if match:
            return i
        end
        set i = i + 1
    end
    
    return -1
end

# String to uppercase
fun string_to_upper(str):
    if str == null:
        return ""
    end
    
    set result = ""
    set i = 0
    while i < length(str):
        set ch = str[i]
        if ch >= 'a' && ch <= 'z':
            set ch = ch - 'a' + 'A'
        end
        set result = result + ch
        set i = i + 1
    end
    
    return result
end

# String to lowercase
fun string_to_lower(str):
    if str == null:
        return ""
    end
    
    set result = ""
    set i = 0
    while i < length(str):
        set ch = str[i]
        if ch >= 'A' && ch <= 'Z':
            set ch = ch - 'A' + 'a'
        end
        set result = result + ch
        set i = i + 1
    end
    
    return result
end

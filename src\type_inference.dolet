# Dolet Self-Hosting Type Inference System
# Static type inference for ultra-fast compilation without runtime overhead
# Analyzes AST nodes and infers types for all expressions and variables

# Import type constants from symbol_table.dolet
# TYPE_* constants are defined there

# Type inference context
set type_context = []
set inference_errors = []

# Initialize type inference
fun init_type_inference():
    set type_context = []
    set inference_errors = []
end

# Add type to context
fun add_type_to_context(name, inferred_type):
    set type_context = type_context + [[name, inferred_type]]
end

# Get type from context
fun get_type_from_context(name):
    set i = 0
    while i < array_length(type_context):
        set entry = type_context[i]
        if entry[0] == name:
            return entry[1]
        end
        set i = i + 1
    end
    return TYPE_UNKNOWN
end

# Add inference error
fun add_inference_error(message, line, column):
    set error = [message, line, column]
    set inference_errors = inference_errors + [error]
end

# Check if there are inference errors
fun has_inference_errors():
    return array_length(inference_errors) > 0
end

# Print inference errors
fun print_inference_errors():
    if !has_inference_errors():
        return
    end
    
    say "Type Inference Errors:"
    set i = 0
    while i < array_length(inference_errors):
        set error = inference_errors[i]
        say "  Line " + error[1] + ", Column " + error[2] + ": " + error[0]
        set i = i + 1
    end
end

# Infer type from literal value
fun infer_literal_type(value):
    if value == null:
        return TYPE_NULL
    end
    if value == true || value == false:
        return TYPE_BOOL
    end
    
    # Check if it's a string (simple heuristic)
    set str_test = value + ""
    if str_test == value && length(str_test) > 0:
        # Check if it's a single character
        if length(str_test) == 1:
            return TYPE_CHAR
        end
        return TYPE_STRING
    end
    
    # Check if it's a number
    if value == 0 || value != 0:
        # Check if it has decimal places (simple check)
        set int_value = value
        if value == int_value:
            return TYPE_INT
        else:
            return TYPE_FLOAT
        end
    end
    
    return TYPE_UNKNOWN
end

# Infer type from binary operation
fun infer_binary_type(left_type, operator, right_type):
    # Arithmetic operations
    if operator == OP_ADD || operator == OP_SUBTRACT || operator == OP_MULTIPLY || operator == OP_DIVIDE || operator == OP_MODULO:
        # String concatenation
        if left_type == TYPE_STRING || right_type == TYPE_STRING:
            return TYPE_STRING
        end
        
        # Numeric operations
        if left_type == TYPE_DOUBLE || right_type == TYPE_DOUBLE:
            return TYPE_DOUBLE
        end
        if left_type == TYPE_FLOAT || right_type == TYPE_FLOAT:
            return TYPE_FLOAT
        end
        if left_type == TYPE_INT && right_type == TYPE_INT:
            return TYPE_INT
        end
        
        # Mixed numeric types default to float
        if (left_type == TYPE_INT || left_type == TYPE_FLOAT || left_type == TYPE_DOUBLE) &&
           (right_type == TYPE_INT || right_type == TYPE_FLOAT || right_type == TYPE_DOUBLE):
            return TYPE_FLOAT
        end
        
        return TYPE_UNKNOWN
    end
    
    # Comparison operations always return bool
    if operator == OP_EQUAL || operator == OP_NOT_EQUAL || operator == OP_LESS || 
       operator == OP_LESS_EQUAL || operator == OP_GREATER || operator == OP_GREATER_EQUAL:
        return TYPE_BOOL
    end
    
    # Logical operations
    if operator == OP_AND || operator == OP_OR:
        if left_type == TYPE_BOOL && right_type == TYPE_BOOL:
            return TYPE_BOOL
        end
        return TYPE_UNKNOWN
    end
    
    return TYPE_UNKNOWN
end

# Infer type from unary operation
fun infer_unary_type(operator, operand_type):
    if operator == OP_NEGATE:
        if operand_type == TYPE_INT || operand_type == TYPE_FLOAT || operand_type == TYPE_DOUBLE:
            return operand_type
        end
        return TYPE_UNKNOWN
    end
    
    if operator == OP_NOT:
        if operand_type == TYPE_BOOL:
            return TYPE_BOOL
        end
        return TYPE_UNKNOWN
    end
    
    return TYPE_UNKNOWN
end

# Infer type from AST expression
fun infer_expression_type(expr):
    if expr == null:
        return TYPE_NULL
    end
    
    set ast_type = get_ast_type(expr)
    set ast_data = get_ast_data(expr)
    
    if ast_type == AST_LITERAL_EXPR:
        set value = ast_data[0]
        return infer_literal_type(value)
    end
    
    if ast_type == AST_IDENTIFIER_EXPR:
        set name = ast_data[0]
        # Look up in symbol table or type context
        set symbol = lookup_symbol(name)
        if symbol != null:
            return get_symbol_type(symbol)
        end
        return get_type_from_context(name)
    end
    
    if ast_type == AST_BINARY_EXPR:
        set left_expr = ast_data[0]
        set operator = ast_data[1]
        set right_expr = ast_data[2]
        
        set left_type = infer_expression_type(left_expr)
        set right_type = infer_expression_type(right_expr)
        
        return infer_binary_type(left_type, operator, right_type)
    end
    
    if ast_type == AST_UNARY_EXPR:
        set operator = ast_data[0]
        set operand_expr = ast_data[1]
        
        set operand_type = infer_expression_type(operand_expr)
        return infer_unary_type(operator, operand_type)
    end
    
    if ast_type == AST_CALL_EXPR:
        set callee_expr = ast_data[0]
        set arguments = ast_data[1]
        
        # For built-in functions, we know their return types
        if get_ast_type(callee_expr) == AST_IDENTIFIER_EXPR:
            set func_name = get_ast_data(callee_expr)[0]
            return infer_builtin_function_type(func_name, arguments)
        end
        
        # For user-defined functions, look up in symbol table
        return TYPE_UNKNOWN
    end
    
    if ast_type == AST_ASSIGN_EXPR:
        set target_expr = ast_data[0]
        set value_expr = ast_data[1]
        
        # Assignment returns the type of the assigned value
        return infer_expression_type(value_expr)
    end
    
    if ast_type == AST_ARRAY_EXPR:
        set elements = ast_data[0]
        if array_length(elements) > 0:
            # Array type is based on first element
            set element_type = infer_expression_type(elements[0])
            return TYPE_ARRAY  # Could be extended to include element type
        end
        return TYPE_ARRAY
    end
    
    return TYPE_UNKNOWN
end

# Infer type for built-in functions
fun infer_builtin_function_type(func_name, arguments):
    if func_name == "length":
        return TYPE_INT
    end
    if func_name == "string_to_int":
        return TYPE_INT
    end
    if func_name == "int_to_string":
        return TYPE_STRING
    end
    if func_name == "string_to_float":
        return TYPE_FLOAT
    end
    if func_name == "float_to_string":
        return TYPE_STRING
    end
    if func_name == "abs_int":
        return TYPE_INT
    end
    if func_name == "abs_float":
        return TYPE_FLOAT
    end
    if func_name == "max_int" || func_name == "min_int":
        return TYPE_INT
    end
    if func_name == "max_float" || func_name == "min_float":
        return TYPE_FLOAT
    end
    if func_name == "sqrt_float":
        return TYPE_FLOAT
    end
    if func_name == "array_length":
        return TYPE_INT
    end
    if func_name == "array_get":
        return TYPE_UNKNOWN  # Depends on array element type
    end
    if func_name == "array_contains":
        return TYPE_BOOL
    end
    if func_name == "string_contains":
        return TYPE_BOOL
    end
    if func_name == "string_equals":
        return TYPE_BOOL
    end
    
    return TYPE_UNKNOWN
end

# Infer types for variable declaration
fun infer_var_decl_type(var_decl):
    set ast_data = get_ast_data(var_decl)
    set name = ast_data[0]
    set type_annotation = ast_data[1]
    set initializer = ast_data[2]
    set is_const = ast_data[3]
    
    set inferred_type = TYPE_UNKNOWN
    
    # If there's a type annotation, use it
    if type_annotation != null:
        set inferred_type = type_name_to_constant(type_annotation)
    end
    
    # If there's an initializer, infer from it
    if initializer != null:
        set init_type = infer_expression_type(initializer)
        
        if type_annotation != null:
            # Check compatibility
            if !types_compatible(inferred_type, init_type):
                add_inference_error("Type mismatch: cannot assign " + type_constant_to_name(init_type) + 
                                   " to " + type_constant_to_name(inferred_type), 0, 0)
            end
        else:
            # Use inferred type from initializer
            set inferred_type = init_type
        end
    end
    
    # Add to type context
    add_type_to_context(name, inferred_type)
    
    return inferred_type
end

# Infer types for function declaration
fun infer_function_type(func_decl):
    set ast_data = get_ast_data(func_decl)
    set name = ast_data[0]
    set parameters = ast_data[1]
    set return_type = ast_data[2]
    set body = ast_data[3]
    
    # Add function to type context
    add_type_to_context(name, TYPE_FUNCTION)
    
    # Process parameters
    set i = 0
    while i < array_length(parameters):
        set param = parameters[i]
        set param_name = param[0]
        set param_type_name = param[1]
        
        set param_type = TYPE_UNKNOWN
        if param_type_name != null:
            set param_type = type_name_to_constant(param_type_name)
        end
        
        add_type_to_context(param_name, param_type)
        set i = i + 1
    end
    
    # Infer types for function body
    set i = 0
    while i < array_length(body):
        infer_statement_type(body[i])
        set i = i + 1
    end
    
    return TYPE_FUNCTION
end

# Infer types for statement
fun infer_statement_type(stmt):
    if stmt == null:
        return TYPE_NULL
    end
    
    set ast_type = get_ast_type(stmt)
    
    if ast_type == AST_VAR_DECL:
        return infer_var_decl_type(stmt)
    end
    
    if ast_type == AST_FUN_DECL:
        return infer_function_type(stmt)
    end
    
    if ast_type == AST_EXPRESSION_STMT:
        set ast_data = get_ast_data(stmt)
        set expr = ast_data[0]
        return infer_expression_type(expr)
    end
    
    if ast_type == AST_SAY_STMT:
        set ast_data = get_ast_data(stmt)
        set expr = ast_data[0]
        infer_expression_type(expr)  # Check expression but say returns void
        return TYPE_NULL
    end
    
    if ast_type == AST_RETURN_STMT:
        set ast_data = get_ast_data(stmt)
        set value_expr = ast_data[0]
        if value_expr != null:
            return infer_expression_type(value_expr)
        end
        return TYPE_NULL
    end
    
    if ast_type == AST_IF_STMT:
        set ast_data = get_ast_data(stmt)
        set condition = ast_data[0]
        set then_branch = ast_data[1]
        set else_branch = ast_data[2]
        
        # Check condition is boolean
        set condition_type = infer_expression_type(condition)
        if condition_type != TYPE_BOOL:
            add_inference_error("If condition must be boolean, got " + type_constant_to_name(condition_type), 0, 0)
        end
        
        # Infer types for branches
        set i = 0
        while i < array_length(then_branch):
            infer_statement_type(then_branch[i])
            set i = i + 1
        end
        
        if else_branch != null:
            set i = 0
            while i < array_length(else_branch):
                infer_statement_type(else_branch[i])
                set i = i + 1
            end
        end
        
        return TYPE_NULL
    end
    
    if ast_type == AST_WHILE_STMT:
        set ast_data = get_ast_data(stmt)
        set condition = ast_data[0]
        set body = ast_data[1]
        
        # Check condition is boolean
        set condition_type = infer_expression_type(condition)
        if condition_type != TYPE_BOOL:
            add_inference_error("While condition must be boolean, got " + type_constant_to_name(condition_type), 0, 0)
        end
        
        # Infer types for body
        set i = 0
        while i < array_length(body):
            infer_statement_type(body[i])
            set i = i + 1
        end
        
        return TYPE_NULL
    end
    
    return TYPE_UNKNOWN
end

# Main type inference function
fun infer_program_types(ast):
    init_type_inference()
    
    if ast == null:
        return false
    end
    
    set ast_type = get_ast_type(ast)
    if ast_type != AST_PROGRAM:
        add_inference_error("Expected program AST node", 0, 0)
        return false
    end
    
    set statements = get_ast_data(ast)
    set i = 0
    while i < array_length(statements):
        infer_statement_type(statements[i])
        set i = i + 1
    end
    
    if has_inference_errors():
        print_inference_errors()
        return false
    end
    
    return true
end

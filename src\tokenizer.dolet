# Dolet Self-Hosting Tokenizer
# High-performance lexical analyzer for the Dolet programming language
# Converts source code into tokens for parsing

# Token types enumeration
const TOKEN_INTEGER = 1
const TOKEN_FLOAT = 2
const TOKEN_DOUBLE = 3
const TOKEN_STRING = 4
const TOKEN_CHAR = 5
const TOKEN_BOOLEAN = 6
const TOKEN_NULL = 7
const TOKEN_IDENTIFIER = 8

# Keywords
const TOKEN_SET = 10
const TOKEN_CONST = 11
const TOKEN_FUN = 12
const TOKEN_END = 13
const TOKEN_IF = 14
const TOKEN_ELSE = 15
const TOKEN_FOR = 16
const TOKEN_WHILE = 17
const TOKEN_DO = 18
const TOKEN_RETURN = 19
const TOKEN_TRUE = 20
const TOKEN_FALSE = 21

# Built-in functions
const TOKEN_SAY = 30
const TOKEN_ASK = 31
const TOKEN_INPUT = 32

# Operators
const TOKEN_PLUS = 40
const TOKEN_MINUS = 41
const TOKEN_STAR = 42
const TOKEN_SLASH = 43
const TOKEN_PERCENT = 44
const TOKEN_ASSIGN = 45
const TOKEN_PLUS_ASSIGN = 46
const TOKEN_MINUS_ASSIGN = 47
const TOKEN_STAR_ASSIGN = 48
const TOKEN_SLASH_ASSIGN = 49
const TOKEN_EQUAL_EQUAL = 50
const TOKEN_BANG_EQUAL = 51
const TOKEN_LESS = 52
const TOKEN_LESS_EQUAL = 53
const TOKEN_GREATER = 54
const TOKEN_GREATER_EQUAL = 55
const TOKEN_AND = 56
const TOKEN_OR = 57
const TOKEN_NOT = 58

# Delimiters
const TOKEN_LEFT_PAREN = 60
const TOKEN_RIGHT_PAREN = 61
const TOKEN_LEFT_BRACKET = 62
const TOKEN_RIGHT_BRACKET = 63
const TOKEN_LEFT_BRACE = 64
const TOKEN_RIGHT_BRACE = 65
const TOKEN_COMMA = 66
const TOKEN_COLON = 67
const TOKEN_SEMICOLON = 68
const TOKEN_DOT = 69

# Special tokens
const TOKEN_NEWLINE = 70
const TOKEN_EOF = 71
const TOKEN_COMMENT = 72
const TOKEN_WHITESPACE = 73

# Token structure
# Each token has: type, lexeme, line, column
fun create_token(token_type, lexeme, line, column):
    set token = [token_type, lexeme, line, column]
    return token
end

fun get_token_type(token):
    return token[0]
end

fun get_token_lexeme(token):
    return token[1]
end

fun get_token_line(token):
    return token[2]
end

fun get_token_column(token):
    return token[3]
end

# Tokenizer state
set source = ""
set current = 0
set line = 1
set column = 1
set tokens = []

# Initialize tokenizer with source code
fun init_tokenizer(source_code):
    set source = source_code
    set current = 0
    set line = 1
    set column = 1
    set tokens = []
end

# Check if we're at end of source
fun is_at_end():
    return current >= length(source)
end

# Get current character
fun peek():
    if is_at_end():
        return '\0'
    end
    return source[current]
end

# Get next character
fun peek_next():
    if current + 1 >= length(source):
        return '\0'
    end
    return source[current + 1]
end

# Advance to next character
fun advance():
    if is_at_end():
        return '\0'
    end
    set ch = source[current]
    set current = current + 1
    if ch == '\n':
        set line = line + 1
        set column = 1
    else:
        set column = column + 1
    end
    return ch
end

# Check if character matches
fun match_char(expected):
    if is_at_end():
        return false
    end
    if source[current] != expected:
        return false
    end
    set current = current + 1
    set column = column + 1
    return true
end

# Skip whitespace
fun skip_whitespace():
    while !is_at_end():
        set ch = peek()
        if ch == ' ' || ch == '\r' || ch == '\t':
            advance()
        else:
            return
        end
    end
end

# Check if character is digit
fun is_digit(ch):
    return ch >= '0' && ch <= '9'
end

# Check if character is alpha
fun is_alpha(ch):
    return (ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z') || ch == '_'
end

# Check if character is alphanumeric
fun is_alphanumeric(ch):
    return is_alpha(ch) || is_digit(ch)
end

# Scan string literal
fun scan_string():
    set start_line = line
    set start_column = column - 1
    set value = ""
    
    while peek() != '"' && !is_at_end():
        if peek() == '\n':
            set line = line + 1
            set column = 1
        end
        set value = value + advance()
    end
    
    if is_at_end():
        say "Error: Unterminated string at line " + start_line
        return null
    end
    
    # Consume closing quote
    advance()
    
    return create_token(TOKEN_STRING, value, start_line, start_column)
end

# Scan character literal
fun scan_char():
    set start_line = line
    set start_column = column - 1
    
    if is_at_end():
        say "Error: Unterminated character at line " + start_line
        return null
    end
    
    set value = advance()
    
    if peek() != '\'':
        say "Error: Unterminated character at line " + start_line
        return null
    end
    
    # Consume closing quote
    advance()
    
    return create_token(TOKEN_CHAR, value, start_line, start_column)
end

# Scan number literal
fun scan_number():
    set start_line = line
    set start_column = column - 1
    set value = ""
    
    # Scan integer part
    while is_digit(peek()):
        set value = value + advance()
    end
    
    # Check for decimal point
    if peek() == '.' && is_digit(peek_next()):
        set value = value + advance()  # consume '.'
        while is_digit(peek()):
            set value = value + advance()
        end
        
        # Determine if float or double based on precision
        if length(value) > 7:  # More than 7 digits suggests double precision
            return create_token(TOKEN_DOUBLE, value, start_line, start_column)
        else:
            return create_token(TOKEN_FLOAT, value, start_line, start_column)
        end
    end
    
    return create_token(TOKEN_INTEGER, value, start_line, start_column)
end

# Scan identifier or keyword
fun scan_identifier():
    set start_line = line
    set start_column = column - 1
    set value = ""
    
    while is_alphanumeric(peek()):
        set value = value + advance()
    end
    
    # Check if it's a keyword
    set token_type = get_keyword_type(value)
    if token_type == 0:
        set token_type = TOKEN_IDENTIFIER
    end
    
    return create_token(token_type, value, start_line, start_column)
end

# Get keyword token type
fun get_keyword_type(text):
    if text == "set":
        return TOKEN_SET
    end
    if text == "const":
        return TOKEN_CONST
    end
    if text == "fun":
        return TOKEN_FUN
    end
    if text == "end":
        return TOKEN_END
    end
    if text == "if":
        return TOKEN_IF
    end
    if text == "else":
        return TOKEN_ELSE
    end
    if text == "for":
        return TOKEN_FOR
    end
    if text == "while":
        return TOKEN_WHILE
    end
    if text == "do":
        return TOKEN_DO
    end
    if text == "return":
        return TOKEN_RETURN
    end
    if text == "true":
        return TOKEN_TRUE
    end
    if text == "false":
        return TOKEN_FALSE
    end
    if text == "say":
        return TOKEN_SAY
    end
    if text == "ask":
        return TOKEN_ASK
    end
    if text == "input":
        return TOKEN_INPUT
    end
    return 0  # Not a keyword
end

# Main tokenization function
fun tokenize():
    while !is_at_end():
        skip_whitespace()
        
        if is_at_end():
            return tokens
        end
        
        set start_line = line
        set start_column = column
        set ch = advance()
        
        # Single character tokens
        if ch == '(':
            set tokens = tokens + [create_token(TOKEN_LEFT_PAREN, "(", start_line, start_column)]
        else if ch == ')':
            set tokens = tokens + [create_token(TOKEN_RIGHT_PAREN, ")", start_line, start_column)]
        else if ch == '[':
            set tokens = tokens + [create_token(TOKEN_LEFT_BRACKET, "[", start_line, start_column)]
        else if ch == ']':
            set tokens = tokens + [create_token(TOKEN_RIGHT_BRACKET, "]", start_line, start_column)]
        else if ch == '{':
            set tokens = tokens + [create_token(TOKEN_LEFT_BRACE, "{", start_line, start_column)]
        else if ch == '}':
            set tokens = tokens + [create_token(TOKEN_RIGHT_BRACE, "}", start_line, start_column)]
        else if ch == ',':
            set tokens = tokens + [create_token(TOKEN_COMMA, ",", start_line, start_column)]
        else if ch == ':':
            set tokens = tokens + [create_token(TOKEN_COLON, ":", start_line, start_column)]
        else if ch == ';':
            set tokens = tokens + [create_token(TOKEN_SEMICOLON, ";", start_line, start_column)]
        else if ch == '.':
            set tokens = tokens + [create_token(TOKEN_DOT, ".", start_line, start_column)]
        else if ch == '\n':
            set tokens = tokens + [create_token(TOKEN_NEWLINE, "\n", start_line, start_column)]
        # Two character tokens and operators
        else if ch == '+':
            if match_char('='):
                set tokens = tokens + [create_token(TOKEN_PLUS_ASSIGN, "+=", start_line, start_column)]
            else:
                set tokens = tokens + [create_token(TOKEN_PLUS, "+", start_line, start_column)]
            end
        else if ch == '-':
            if match_char('='):
                set tokens = tokens + [create_token(TOKEN_MINUS_ASSIGN, "-=", start_line, start_column)]
            else:
                set tokens = tokens + [create_token(TOKEN_MINUS, "-", start_line, start_column)]
            end
        else if ch == '*':
            if match_char('='):
                set tokens = tokens + [create_token(TOKEN_STAR_ASSIGN, "*=", start_line, start_column)]
            else:
                set tokens = tokens + [create_token(TOKEN_STAR, "*", start_line, start_column)]
            end
        else if ch == '/':
            if match_char('='):
                set tokens = tokens + [create_token(TOKEN_SLASH_ASSIGN, "/=", start_line, start_column)]
            else:
                set tokens = tokens + [create_token(TOKEN_SLASH, "/", start_line, start_column)]
            end
        else if ch == '%':
            set tokens = tokens + [create_token(TOKEN_PERCENT, "%", start_line, start_column)]
        else if ch == '=':
            if match_char('='):
                set tokens = tokens + [create_token(TOKEN_EQUAL_EQUAL, "==", start_line, start_column)]
            else:
                set tokens = tokens + [create_token(TOKEN_ASSIGN, "=", start_line, start_column)]
            end
        else if ch == '!':
            if match_char('='):
                set tokens = tokens + [create_token(TOKEN_BANG_EQUAL, "!=", start_line, start_column)]
            else:
                set tokens = tokens + [create_token(TOKEN_NOT, "!", start_line, start_column)]
            end
        else if ch == '<':
            if match_char('='):
                set tokens = tokens + [create_token(TOKEN_LESS_EQUAL, "<=", start_line, start_column)]
            else:
                set tokens = tokens + [create_token(TOKEN_LESS, "<", start_line, start_column)]
            end
        else if ch == '>':
            if match_char('='):
                set tokens = tokens + [create_token(TOKEN_GREATER_EQUAL, ">=", start_line, start_column)]
            else:
                set tokens = tokens + [create_token(TOKEN_GREATER, ">", start_line, start_column)]
            end
        else if ch == '&':
            if match_char('&'):
                set tokens = tokens + [create_token(TOKEN_AND, "&&", start_line, start_column)]
            else:
                say "Error: Unexpected character '&' at line " + start_line + ", column " + start_column
            end
        else if ch == '|':
            if match_char('|'):
                set tokens = tokens + [create_token(TOKEN_OR, "||", start_line, start_column)]
            else:
                say "Error: Unexpected character '|' at line " + start_line + ", column " + start_column
            end
        # Comments
        else if ch == '#':
            # Skip comment until end of line
            while peek() != '\n' && !is_at_end():
                advance()
            end
        # String literals
        else if ch == '"':
            set token = scan_string()
            if token != null:
                set tokens = tokens + [token]
            end
        # Character literals
        else if ch == '\'':
            set token = scan_char()
            if token != null:
                set tokens = tokens + [token]
            end
        # Numbers
        else if is_digit(ch):
            # Put back the character and scan number
            set current = current - 1
            set column = column - 1
            set token = scan_number()
            set tokens = tokens + [token]
        # Identifiers and keywords
        else if is_alpha(ch):
            # Put back the character and scan identifier
            set current = current - 1
            set column = column - 1
            set token = scan_identifier()
            set tokens = tokens + [token]
        else:
            say "Error: Unexpected character '" + ch + "' at line " + start_line + ", column " + start_column
        end
    end
    
    # Add EOF token
    set tokens = tokens + [create_token(TOKEN_EOF, "", line, column)]
    return tokens
end

# Get all tokens from source code
fun get_tokens(source_code):
    init_tokenizer(source_code)
    return tokenize()
end

# Helper function to check if we have a built-in array size function
# This is a placeholder - in real implementation this would be a native function
fun array_size(arr):
    # This would be implemented at machine code level
    # For bootstrap, we'll use a simple counter approach
    set count = 0
    set i = 0
    while i < 1000000:  # Safety limit
        # Try to access element - if it fails, we've reached the end
        # In real implementation, this would be a bounds check
        set count = count + 1
        set i = i + 1
        if i >= 1000:  # Temporary limit for bootstrap
            return count
        end
    end
    return count
end

use std::io::{self, Write};

fn test_function() -> bool {
    println!("{}", "Function works");
    return true;
    false
}


fn main() {
    println!("{}", "Testing basic syntax");
    let mut x = 42;
    println!("{}", format!("{}{}" , "x = ", x));
    let mut arr = vec![1, 2, 3];
    println!("{}", "Array created");
    let mut result = test_function();
    println!("{}", format!("{}{}" , "Function result: ", result));
    println!("{}", "All tests complete");
}

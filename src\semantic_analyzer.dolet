# Dolet Self-Hosting Semantic Analyzer
# Validates program semantics, type checking, and scope resolution
# Ensures program correctness before code generation

# Semantic analysis state
set semantic_errors = []
set current_function = null
set in_loop = false
set return_type_stack = []

# Initialize semantic analyzer
fun init_semantic_analyzer():
    set semantic_errors = []
    set current_function = null
    set in_loop = false
    set return_type_stack = []
end

# Add semantic error
fun add_semantic_error(message, line, column):
    set error = [message, line, column]
    set semantic_errors = semantic_errors + [error]
end

# Check if there are semantic errors
fun has_semantic_errors():
    return array_length(semantic_errors) > 0
end

# Print semantic errors
fun print_semantic_errors():
    if !has_semantic_errors():
        return
    end
    
    say "Semantic Analysis Errors:"
    set i = 0
    while i < array_length(semantic_errors):
        set error = semantic_errors[i]
        say "  Line " + error[1] + ", Column " + error[2] + ": " + error[0]
        set i = i + 1
    end
end

# Push return type for function
fun push_return_type(return_type):
    set return_type_stack = return_type_stack + [return_type]
end

# Pop return type
fun pop_return_type():
    if array_length(return_type_stack) > 0:
        set new_stack = []
        set i = 0
        while i < array_length(return_type_stack) - 1:
            set new_stack = new_stack + [return_type_stack[i]]
            set i = i + 1
        end
        set return_type_stack = new_stack
    end
end

# Get current expected return type
fun get_current_return_type():
    set len = array_length(return_type_stack)
    if len > 0:
        return return_type_stack[len - 1]
    end
    return TYPE_NULL
end

# Analyze expression semantics
fun analyze_expression(expr):
    if expr == null:
        return TYPE_NULL
    end
    
    set ast_type = get_ast_type(expr)
    set ast_data = get_ast_data(expr)
    
    if ast_type == AST_LITERAL_EXPR:
        # Literals are always valid
        set value = ast_data[0]
        return infer_literal_type(value)
    end
    
    if ast_type == AST_IDENTIFIER_EXPR:
        set name = ast_data[0]
        set symbol = lookup_symbol(name)
        
        if symbol == null:
            add_semantic_error("Undefined variable '" + name + "'", 0, 0)
            return TYPE_UNKNOWN
        end
        
        if !get_symbol_is_initialized(symbol):
            add_semantic_error("Variable '" + name + "' used before initialization", 0, 0)
        end
        
        return get_symbol_type(symbol)
    end
    
    if ast_type == AST_BINARY_EXPR:
        set left_expr = ast_data[0]
        set operator = ast_data[1]
        set right_expr = ast_data[2]
        
        set left_type = analyze_expression(left_expr)
        set right_type = analyze_expression(right_expr)
        
        # Check operator compatibility
        set result_type = infer_binary_type(left_type, operator, right_type)
        
        if result_type == TYPE_UNKNOWN:
            add_semantic_error("Invalid operation between " + type_constant_to_name(left_type) + 
                             " and " + type_constant_to_name(right_type), 0, 0)
        end
        
        return result_type
    end
    
    if ast_type == AST_UNARY_EXPR:
        set operator = ast_data[0]
        set operand_expr = ast_data[1]
        
        set operand_type = analyze_expression(operand_expr)
        set result_type = infer_unary_type(operator, operand_type)
        
        if result_type == TYPE_UNKNOWN:
            add_semantic_error("Invalid unary operation on " + type_constant_to_name(operand_type), 0, 0)
        end
        
        return result_type
    end
    
    if ast_type == AST_ASSIGN_EXPR:
        set target_expr = ast_data[0]
        set value_expr = ast_data[1]
        
        # Target must be an identifier
        if get_ast_type(target_expr) != AST_IDENTIFIER_EXPR:
            add_semantic_error("Invalid assignment target", 0, 0)
            return TYPE_UNKNOWN
        end
        
        set target_name = get_ast_data(target_expr)[0]
        set symbol = lookup_symbol(target_name)
        
        if symbol == null:
            add_semantic_error("Undefined variable '" + target_name + "'", 0, 0)
            return TYPE_UNKNOWN
        end
        
        if get_symbol_is_const(symbol):
            add_semantic_error("Cannot assign to constant '" + target_name + "'", 0, 0)
            return TYPE_UNKNOWN
        end
        
        set value_type = analyze_expression(value_expr)
        set target_type = get_symbol_type(symbol)
        
        if !types_compatible(target_type, value_type):
            add_semantic_error("Type mismatch: cannot assign " + type_constant_to_name(value_type) + 
                             " to " + type_constant_to_name(target_type), 0, 0)
        end
        
        return value_type
    end
    
    if ast_type == AST_CALL_EXPR:
        set callee_expr = ast_data[0]
        set arguments = ast_data[1]
        
        # Check if callee is a valid function
        if get_ast_type(callee_expr) == AST_IDENTIFIER_EXPR:
            set func_name = get_ast_data(callee_expr)[0]
            
            # Check built-in functions
            if is_builtin_function(func_name):
                return analyze_builtin_call(func_name, arguments)
            end
            
            # Check user-defined functions
            set symbol = lookup_symbol(func_name)
            if symbol == null:
                add_semantic_error("Undefined function '" + func_name + "'", 0, 0)
                return TYPE_UNKNOWN
            end
            
            if get_symbol_type(symbol) != TYPE_FUNCTION:
                add_semantic_error("'" + func_name + "' is not a function", 0, 0)
                return TYPE_UNKNOWN
            end
            
            # Analyze arguments
            set i = 0
            while i < array_length(arguments):
                analyze_expression(arguments[i])
                set i = i + 1
            end
            
            return TYPE_UNKNOWN  # Would need function signature info
        end
        
        add_semantic_error("Invalid function call", 0, 0)
        return TYPE_UNKNOWN
    end
    
    if ast_type == AST_ARRAY_EXPR:
        set elements = ast_data[0]
        
        if array_length(elements) == 0:
            return TYPE_ARRAY
        end
        
        # Check all elements have compatible types
        set first_type = analyze_expression(elements[0])
        set i = 1
        while i < array_length(elements):
            set element_type = analyze_expression(elements[i])
            if !types_compatible(first_type, element_type):
                add_semantic_error("Array elements must have compatible types", 0, 0)
            end
            set i = i + 1
        end
        
        return TYPE_ARRAY
    end
    
    return TYPE_UNKNOWN
end

# Check if function is built-in
fun is_builtin_function(name):
    if name == "length" || name == "string_to_int" || name == "int_to_string" ||
       name == "string_to_float" || name == "float_to_string" ||
       name == "abs_int" || name == "abs_float" || name == "max_int" || name == "min_int" ||
       name == "max_float" || name == "min_float" || name == "sqrt_float" ||
       name == "array_length" || name == "array_get" || name == "array_contains" ||
       name == "string_contains" || name == "string_equals":
        return true
    end
    return false
end

# Analyze built-in function call
fun analyze_builtin_call(func_name, arguments):
    set arg_count = array_length(arguments)
    
    if func_name == "length":
        if arg_count != 1:
            add_semantic_error("length() expects 1 argument, got " + arg_count, 0, 0)
            return TYPE_INT
        end
        set arg_type = analyze_expression(arguments[0])
        if arg_type != TYPE_STRING && arg_type != TYPE_ARRAY:
            add_semantic_error("length() expects string or array argument", 0, 0)
        end
        return TYPE_INT
    end
    
    if func_name == "string_to_int":
        if arg_count != 1:
            add_semantic_error("string_to_int() expects 1 argument, got " + arg_count, 0, 0)
            return TYPE_INT
        end
        set arg_type = analyze_expression(arguments[0])
        if arg_type != TYPE_STRING:
            add_semantic_error("string_to_int() expects string argument", 0, 0)
        end
        return TYPE_INT
    end
    
    if func_name == "int_to_string":
        if arg_count != 1:
            add_semantic_error("int_to_string() expects 1 argument, got " + arg_count, 0, 0)
            return TYPE_STRING
        end
        set arg_type = analyze_expression(arguments[0])
        if arg_type != TYPE_INT:
            add_semantic_error("int_to_string() expects int argument", 0, 0)
        end
        return TYPE_STRING
    end
    
    if func_name == "array_length":
        if arg_count != 1:
            add_semantic_error("array_length() expects 1 argument, got " + arg_count, 0, 0)
            return TYPE_INT
        end
        set arg_type = analyze_expression(arguments[0])
        if arg_type != TYPE_ARRAY:
            add_semantic_error("array_length() expects array argument", 0, 0)
        end
        return TYPE_INT
    end
    
    # Add more built-in function checks as needed
    
    return infer_builtin_function_type(func_name, arguments)
end

# Analyze statement semantics
fun analyze_statement(stmt):
    if stmt == null:
        return
    end
    
    set ast_type = get_ast_type(stmt)
    set ast_data = get_ast_data(stmt)
    
    if ast_type == AST_VAR_DECL:
        set name = ast_data[0]
        set type_annotation = ast_data[1]
        set initializer = ast_data[2]
        set is_const = ast_data[3]
        
        # Check if variable already exists in current scope
        if symbol_exists_current_scope(name):
            add_semantic_error("Variable '" + name + "' already declared in current scope", 0, 0)
            return
        end
        
        set var_type = TYPE_UNKNOWN
        if type_annotation != null:
            set var_type = type_name_to_constant(type_annotation)
        end
        
        if initializer != null:
            set init_type = analyze_expression(initializer)
            
            if type_annotation != null:
                if !types_compatible(var_type, init_type):
                    add_semantic_error("Type mismatch in variable declaration", 0, 0)
                end
            else:
                set var_type = init_type
            end
        else if is_const:
            add_semantic_error("Constant '" + name + "' must be initialized", 0, 0)
        end
        
        # Define symbol
        define_symbol(name, var_type, is_const, 0, 0)
        if initializer != null:
            initialize_symbol(name, null, 0, 0)
        end
    end
    
    if ast_type == AST_FUN_DECL:
        set name = ast_data[0]
        set parameters = ast_data[1]
        set return_type_name = ast_data[2]
        set body = ast_data[3]
        
        # Check if function already exists
        if symbol_exists_current_scope(name):
            add_semantic_error("Function '" + name + "' already declared", 0, 0)
            return
        end
        
        # Define function symbol
        define_symbol(name, TYPE_FUNCTION, true, 0, 0)
        initialize_symbol(name, null, 0, 0)
        
        # Enter function scope
        set prev_function = current_function
        set current_function = name
        
        set return_type = TYPE_NULL
        if return_type_name != null:
            set return_type = type_name_to_constant(return_type_name)
        end
        push_return_type(return_type)
        
        push_scope()
        
        # Define parameters
        set i = 0
        while i < array_length(parameters):
            set param = parameters[i]
            set param_name = param[0]
            set param_type_name = param[1]
            
            set param_type = TYPE_UNKNOWN
            if param_type_name != null:
                set param_type = type_name_to_constant(param_type_name)
            end
            
            define_symbol(param_name, param_type, false, 0, 0)
            initialize_symbol(param_name, null, 0, 0)
            set i = i + 1
        end
        
        # Analyze function body
        set i = 0
        while i < array_length(body):
            analyze_statement(body[i])
            set i = i + 1
        end
        
        pop_scope()
        pop_return_type()
        set current_function = prev_function
    end
    
    if ast_type == AST_IF_STMT:
        set condition = ast_data[0]
        set then_branch = ast_data[1]
        set else_branch = ast_data[2]
        
        set condition_type = analyze_expression(condition)
        if condition_type != TYPE_BOOL:
            add_semantic_error("If condition must be boolean", 0, 0)
        end
        
        push_scope()
        set i = 0
        while i < array_length(then_branch):
            analyze_statement(then_branch[i])
            set i = i + 1
        end
        pop_scope()
        
        if else_branch != null:
            push_scope()
            set i = 0
            while i < array_length(else_branch):
                analyze_statement(else_branch[i])
                set i = i + 1
            end
            pop_scope()
        end
    end
    
    if ast_type == AST_WHILE_STMT:
        set condition = ast_data[0]
        set body = ast_data[1]
        
        set condition_type = analyze_expression(condition)
        if condition_type != TYPE_BOOL:
            add_semantic_error("While condition must be boolean", 0, 0)
        end
        
        set prev_in_loop = in_loop
        set in_loop = true
        
        push_scope()
        set i = 0
        while i < array_length(body):
            analyze_statement(body[i])
            set i = i + 1
        end
        pop_scope()
        
        set in_loop = prev_in_loop
    end
    
    if ast_type == AST_RETURN_STMT:
        if current_function == null:
            add_semantic_error("Return statement outside function", 0, 0)
            return
        end
        
        set value_expr = ast_data[0]
        set expected_type = get_current_return_type()
        
        if value_expr != null:
            set return_type = analyze_expression(value_expr)
            if !types_compatible(expected_type, return_type):
                add_semantic_error("Return type mismatch", 0, 0)
            end
        else if expected_type != TYPE_NULL:
            add_semantic_error("Function must return a value", 0, 0)
        end
    end
    
    if ast_type == AST_EXPRESSION_STMT:
        set expr = ast_data[0]
        analyze_expression(expr)
    end
    
    if ast_type == AST_SAY_STMT:
        set expr = ast_data[0]
        analyze_expression(expr)
    end
end

# Main semantic analysis function
fun analyze_program(ast):
    init_semantic_analyzer()
    init_symbol_table()
    
    if ast == null:
        add_semantic_error("No AST to analyze", 0, 0)
        return false
    end
    
    set ast_type = get_ast_type(ast)
    if ast_type != AST_PROGRAM:
        add_semantic_error("Expected program AST node", 0, 0)
        return false
    end
    
    set statements = get_ast_data(ast)
    set i = 0
    while i < array_length(statements):
        analyze_statement(statements[i])
        set i = i + 1
    end
    
    if has_semantic_errors():
        print_semantic_errors()
        return false
    end
    
    return true
end

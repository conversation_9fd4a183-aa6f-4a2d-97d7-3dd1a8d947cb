
# 🚀 Dolet-SFI Self-Hosting Master Plan (Updated Structure)

📅 Updated Plan and Instructions for Augment AI team — full clean structure for professional self-hosted compiler architecture.

---

## 🎯 Current Repository Structure

```bash
/Dolet-SFI
  /dolet-bootstrap-compiler/   --> Rust-based bootstrap compiler (frozen)
  /src/                        --> Empty: for the new Dolet self-hosted compiler source
  /examples/                   --> For all Dolet program tests and language examples
  /dolet-vscode-extension/     --> Editor extension for VSCode
  Cargo.toml, README.md, DOLET_GUIDE.md, etc...
```

---

## 🎯 Master Goal Summary

- Write the **new Dolet compiler** inside `/src/` using the Dolet language itself.
- Keep `/dolet-bootstrap-compiler/` frozen and untouched. This is the backup Rust compiler that will help during bootstrap.
- All tests and test programs must go to `/examples/` to keep project clean.

---

## 🔧 Augment AI Work Instructions

### ✅ 1️⃣ Compiler Development Path

| Folder | Purpose | Who Works Here? |
| ------ | ------- | --------------- |
| `/src/` | Self-hosted Dolet compiler source files | Augment AI |
| `/dolet-bootstrap-compiler/` | Existing bootstrap compiler in Rust | **DO NOT MODIFY** |
| `/examples/` | Only for running & testing Dolet programs | Augment AI (for tests only) |

---

### ✅ 2️⃣ What to Build Inside `/src/`

| File | Description |
| ---- | ----------- |
| `tokenizer.dolet` | Tokenizer logic |
| `parser.dolet` | Full parser |
| `symbol_table.dolet` | Symbol table logic |
| `type_inference.dolet` | Token-based type inference logic |
| `semantic_analyzer.dolet` | Semantic rules and validation |
| `codegen.dolet` | Direct machine code generator |
| `error_handling.dolet` | Full error system |
| `memory_pool.dolet` | Arena/bump allocator |
| `main.dolet` | Entry point of compiler logic |

---

### ✅ 3️⃣ Runtime Development

| Folder | Description |
| ------ | ----------- |
| `/src/runtime/` | Runtime code written in Dolet itself |
| Files inside: |
| `string_ops.dolet` | String operations |
| `math_ops.dolet` | Math operations |
| `array_ops.dolet` | Array/list helpers |
| `file_ops.dolet` | File I/O support |
| `error_ops.dolet` | Error handling utilities |

---

### ✅ 4️⃣ Testing

- All Dolet test programs go inside `/examples/` only.
- DO NOT mix test files inside `/src/` to keep clean separation.
- Example tests:
```bash
/examples/hello_world.dolet
/examples/arithmetic_test.dolet
/examples/loop_test.dolet
/examples/file_io_test.dolet
```

---

## 🔧 5️⃣ Bootstrap Flow Reminder

| Stage | Action |
| ----- | ------ |
| Step 1 | Use `dolet-bootstrap.exe` from `/dolet-bootstrap-compiler/` to compile the first Dolet self-hosted compiler. |
| Step 2 | Generate `stage1.exe` |
| Step 3 | Use `stage1.exe` to recompile itself into `stage2.exe` |
| Step 4 | Compare stage1 and stage2 → if identical → ✅ Fully Self-Hosted. |

---

## 🔧 6️⃣ DO NOT DO List

- ❌ DO NOT modify anything inside `/dolet-bootstrap-compiler/`
- ❌ DO NOT add any test files inside `/src/`
- ❌ DO NOT implement dynamic typing or garbage collection (keep static type inference)
- ❌ DO NOT introduce runtime reflection or unsafe operations

---

## 🏁 Final Notes

✅ Follow this architecture strictly to avoid conflicts, preserve bootstrap logic, and guarantee full self-hosting correctness.

🧑‍💻 Generated for Dolet-SFI Self-Hosting Project (2025-06-17)

# Dolet AST Merger
# يدمج ASTs من ملفات متعددة في AST واحد موحد

# Merged AST state
set merged_ast = null
set module_asts = []
set merge_errors = []

# Initialize AST merger
fun init_ast_merger():
    set merged_ast = null
    set module_asts = []
    set merge_errors = []
end

# Module AST entry structure
# [module_name, file_path, ast, merged]
fun create_module_ast_entry(module_name, file_path, ast):
    return [module_name, file_path, ast, false]
end

# Get module AST properties
fun get_module_ast_name(entry):
    return entry[0]
end

fun get_module_ast_path(entry):
    return entry[1]
end

fun get_module_ast(entry):
    return entry[2]
end

fun is_module_ast_merged(entry):
    return entry[3]
end

fun set_module_ast_merged(entry, merged):
    set entry[3] = merged
end

# Add module AST
fun add_module_ast(module_name, file_path, ast):
    set entry = create_module_ast_entry(module_name, file_path, ast)
    set module_asts = module_asts + [entry]
    say "Added AST for module: " + module_name
    return entry
end

# Create empty program AST
fun create_empty_program_ast():
    return [1, []]  # AST_PROGRAM with empty statements
end

# Get statements from program AST
fun get_program_statements(ast):
    if ast == null:
        return []
    end
    if get_ast_type(ast) != 1:  # AST_PROGRAM
        return []
    end
    return get_ast_data(ast)
end

# Set statements in program AST
fun set_program_statements(ast, statements):
    if ast == null:
        return false
    end
    if get_ast_type(ast) != 1:  # AST_PROGRAM
        return false
    end
    set ast[1] = statements
    return true
end

# Add statement to program AST
fun add_statement_to_program(ast, statement):
    set statements = get_program_statements(ast)
    set new_statements = statements + [statement]
    return set_program_statements(ast, new_statements)
end

# Extract function declarations from AST
fun extract_function_declarations(ast):
    set functions = []
    set statements = get_program_statements(ast)
    
    set i = 0
    while i < array_length(statements):
        set stmt = statements[i]
        if get_ast_type(stmt) == 3:  # AST_FUN_DECL
            set functions = functions + [stmt]
        end
        set i = i + 1
    end
    
    return functions
end

# Extract variable declarations from AST
fun extract_variable_declarations(ast):
    set variables = []
    set statements = get_program_statements(ast)
    
    set i = 0
    while i < array_length(statements):
        set stmt = statements[i]
        if get_ast_type(stmt) == 2:  # AST_VAR_DECL
            set variables = variables + [stmt]
        end
        set i = i + 1
    end
    
    return variables
end

# Extract constant declarations from AST
fun extract_constant_declarations(ast):
    set constants = []
    set statements = get_program_statements(ast)
    
    set i = 0
    while i < array_length(statements):
        set stmt = statements[i]
        if get_ast_type(stmt) == 2:  # AST_VAR_DECL
            set ast_data = get_ast_data(stmt)
            set is_const = ast_data[3]
            if is_const:
                set constants = constants + [stmt]
            end
        end
        set i = i + 1
    end
    
    return constants
end

# Check for duplicate function names
fun check_duplicate_functions(functions):
    set function_names = []
    set duplicates = []
    
    set i = 0
    while i < array_length(functions):
        set func = functions[i]
        set func_data = get_ast_data(func)
        set func_name = func_data[0]
        
        # Check if name already exists
        set j = 0
        set found = false
        while j < array_length(function_names):
            if function_names[j] == func_name:
                set found = true
                set duplicates = duplicates + [func_name]
            end
            set j = j + 1
        end
        
        if !found:
            set function_names = function_names + [func_name]
        end
        set i = i + 1
    end
    
    return duplicates
end

# Check for duplicate variable names
fun check_duplicate_variables(variables):
    set variable_names = []
    set duplicates = []
    
    set i = 0
    while i < array_length(variables):
        set var = variables[i]
        set var_data = get_ast_data(var)
        set var_name = var_data[0]
        
        # Check if name already exists
        set j = 0
        set found = false
        while j < array_length(variable_names):
            if variable_names[j] == var_name:
                set found = true
                set duplicates = duplicates + [var_name]
            end
            set j = j + 1
        end
        
        if !found:
            set variable_names = variable_names + [var_name]
        end
        set i = i + 1
    end
    
    return duplicates
end

# Merge module ASTs in dependency order
fun merge_module_asts(import_order):
    say "Merging module ASTs..."
    
    # Create empty merged AST
    set merged_ast = create_empty_program_ast()
    
    set all_functions = []
    set all_variables = []
    set all_constants = []
    set all_other_statements = []
    
    # Process modules in dependency order
    set i = 0
    while i < array_length(import_order):
        set module_name = import_order[i]
        
        # Find module AST entry
        set entry = null
        set j = 0
        while j < array_length(module_asts):
            set ast_entry = module_asts[j]
            if get_module_ast_name(ast_entry) == module_name:
                set entry = ast_entry
            end
            set j = j + 1
        end
        
        if entry == null:
            say "Warning: No AST found for module: " + module_name
        else:
            set module_ast = get_module_ast(entry)
            
            # Extract different types of declarations
            set functions = extract_function_declarations(module_ast)
            set variables = extract_variable_declarations(module_ast)
            set constants = extract_constant_declarations(module_ast)
            
            # Add to collections
            set all_functions = all_functions + functions
            set all_variables = all_variables + variables
            set all_constants = all_constants + constants
            
            # Mark as merged
            set_module_ast_merged(entry, true)
            say "Merged module: " + module_name
        end
        set i = i + 1
    end
    
    # Check for duplicates
    set duplicate_functions = check_duplicate_functions(all_functions)
    set duplicate_variables = check_duplicate_variables(all_variables)
    
    if array_length(duplicate_functions) > 0:
        say "Warning: Duplicate function names found:"
        set i = 0
        while i < array_length(duplicate_functions):
            say "  - " + duplicate_functions[i]
            set i = i + 1
        end
    end
    
    if array_length(duplicate_variables) > 0:
        say "Warning: Duplicate variable names found:"
        set i = 0
        while i < array_length(duplicate_variables):
            say "  - " + duplicate_variables[i]
            set i = i + 1
        end
    end
    
    # Build final merged statements in correct order
    set merged_statements = []
    
    # Add constants first
    set merged_statements = merged_statements + all_constants
    
    # Add variables
    set merged_statements = merged_statements + all_variables
    
    # Add functions
    set merged_statements = merged_statements + all_functions
    
    # Add other statements
    set merged_statements = merged_statements + all_other_statements
    
    # Set merged statements
    set_program_statements(merged_ast, merged_statements)
    
    say "AST merge complete: " + array_length(merged_statements) + " statements"
    return merged_ast
end

# Validate merged AST
fun validate_merged_ast():
    if merged_ast == null:
        say "Error: No merged AST to validate"
        return false
    end
    
    set statements = get_program_statements(merged_ast)
    if array_length(statements) == 0:
        say "Warning: Merged AST has no statements"
        return false
    end
    
    say "Merged AST validation:"
    say "  Total statements: " + array_length(statements)
    
    # Count different types
    set function_count = 0
    set variable_count = 0
    set constant_count = 0
    set other_count = 0
    
    set i = 0
    while i < array_length(statements):
        set stmt = statements[i]
        set stmt_type = get_ast_type(stmt)
        
        if stmt_type == 3:  # AST_FUN_DECL
            set function_count = function_count + 1
        else if stmt_type == 2:  # AST_VAR_DECL
            set stmt_data = get_ast_data(stmt)
            set is_const = stmt_data[3]
            if is_const:
                set constant_count = constant_count + 1
            else:
                set variable_count = variable_count + 1
            end
        else:
            set other_count = other_count + 1
        end
        set i = i + 1
    end
    
    say "  Functions: " + function_count
    say "  Variables: " + variable_count
    say "  Constants: " + constant_count
    say "  Other: " + other_count
    
    say "Merged AST is valid"
    return true
end

# Get merged AST
fun get_merged_ast():
    return merged_ast
end

# Get module ASTs
fun get_module_asts():
    return module_asts
end

# Print merge information
fun print_merge_info():
    say ""
    say "AST Merge Information:"
    say "====================="
    
    say "Module ASTs:"
    set i = 0
    while i < array_length(module_asts):
        set entry = module_asts[i]
        set name = get_module_ast_name(entry)
        set path = get_module_ast_path(entry)
        set merged = is_module_ast_merged(entry)
        
        say "  " + name + " (" + path + ") - Merged: " + merged
        set i = i + 1
    end
    
    if merged_ast != null:
        set statements = get_program_statements(merged_ast)
        say ""
        say "Merged AST: " + array_length(statements) + " statements"
    else:
        say ""
        say "No merged AST available"
    end
end

# Clear merge state
fun clear_merge_state():
    set merged_ast = null
    set module_asts = []
    set merge_errors = []
end

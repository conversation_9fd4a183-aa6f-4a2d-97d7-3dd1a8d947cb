use std::io::{self, Write};

fn greet(name: &str) {
    println!("{}", format!("{}{}" , format!("{}{}" , "Hello, ", name), "!"));
}


fn main() {
    let mut message = "Hello from Dolet Self-Hosting Compiler!";
    let mut number = 42;
    println!("{}", message);
    println!("{}", format!("{}{}" , "The answer is: ", number));
    let mut a = 10;
    let mut b = 5;
    let mut sum = format!("{}{}" , a, b);
    let mut product = (a * b);
    println!("{}", format!("{}{}" , "Sum: ", sum));
    println!("{}", format!("{}{}" , "Product: ", product));
    greet("Bootstrap");
}
